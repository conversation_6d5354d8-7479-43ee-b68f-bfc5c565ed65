<?php
/**
 * Debug page for field mapping issues
 * Access via: http://your-domain/debug_field_mapping.php
 */

// Bootstrap the application
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

const DEBUG_MODE = true;
const API_RUN = true;
require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

try {
    startup_sequence::start($path,$schema);
    
    echo "<h2>Field Mapping Debug</h2>\n";
    
    // Test the specific field that's not matching
    echo "<h3>Testing 'end_customer_address_1' field matching</h3>\n";
    
    // Clear cache first
    echo "<p>Clearing field mapper cache...</p>\n";
    \system\unified_field_mapper::clear_cache();
    
    // Test field mapping
    $test_columns = ['end_customer_address_1'];
    $suggestions = \system\unified_field_mapper::suggest_field_mappings($test_columns);
    
    echo "<h4>Mapping Results:</h4>\n";
    if (isset($suggestions['end_customer_address_1'])) {
        $suggestion = $suggestions['end_customer_address_1'];
        echo "<p><strong>Match found:</strong> {$suggestion['field_name']}</p>\n";
        echo "<p><strong>Confidence:</strong> {$suggestion['confidence']}</p>\n";
        echo "<p><strong>Final Score:</strong> {$suggestion['final_score']}</p>\n";
        echo "<p><strong>Label:</strong> {$suggestion['label']}</p>\n";
    } else {
        echo "<p><strong>ERROR:</strong> No match found for 'end_customer_address_1'</p>\n";
    }
    
    // Debug all enabled fields
    echo "<h3>All Enabled Fields</h3>\n";
    $debug_info = \system\unified_field_mapper::debug_field_mappings();
    echo "<p><strong>Total enabled fields:</strong> " . count($debug_info) . "</p>\n";
    
    // Check if address field is enabled
    if (isset($debug_info['address'])) {
        echo "<h4>Address Field Details:</h4>\n";
        echo "<pre>" . print_r($debug_info['address'], true) . "</pre>\n";
    } else {
        echo "<p><strong>ERROR:</strong> Address field is not enabled!</p>\n";
    }
    
    // Show all enabled fields
    echo "<h4>All Enabled Fields:</h4>\n";
    echo "<ul>\n";
    foreach ($debug_info as $field_name => $info) {
        echo "<li><strong>{$field_name}</strong> (priority: {$info['priority']}, patterns: " . implode(', ', $info['patterns']) . ")</li>\n";
    }
    echo "</ul>\n";
    
    // Test direct field definition access
    echo "<h3>Direct Field Definition Test</h3>\n";
    $address_field = unified_field_definitions::get_field('address');
    if ($address_field) {
        echo "<h4>Address Field Definition:</h4>\n";
        echo "<pre>" . print_r($address_field, true) . "</pre>\n";
        
        $enabled = $address_field['matching']['enabled'] ?? false;
        echo "<p><strong>Address field enabled in definition:</strong> " . ($enabled ? 'YES' : 'NO') . "</p>\n";
    } else {
        echo "<p><strong>ERROR:</strong> Could not retrieve address field definition!</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p><strong>ERROR:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>
