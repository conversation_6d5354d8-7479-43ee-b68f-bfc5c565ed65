[preview_debug] [2025-08-14 13:12:57] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-14 13:12:57] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-14 13:47:20] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autodesk_accounts] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                    [5] => email\n                    [6] => phone\n                    [7] => account_type\n                    [8] => city\n                    [9] => postal_code\n                    [10] => country\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-14 13:47:20] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 10\n)\n
[preview_debug] [2025-08-15 09:05:02] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [autodesk_subscriptions] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 09:05:02] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `autodesk_subscriptions_id`, `subs`.`subscriptionId` AS `autodesk_subscriptions_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `autodesk_subscriptions_subscriptionReferenceNumber`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` WHERE `subs`.`autoRenew` = 'ON' AND `subs`.`status` = 'Active' GROUP BY `endcust`.`account_csn` LIMIT 10\n)\n
[preview_debug] [2025-08-15 09:05:11] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 09:05:11] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-15 09:26:51] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 09:26:51] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-15 09:26:52] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 09:26:52] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `autobooks_import_bluebeam_data_product_name` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-15 09:26:52] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 09:26:52] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `autobooks_import_bluebeam_data_product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-15 09:26:53] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 09:26:53] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `autobooks_import_bluebeam_data_product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-15 09:26:54] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 09:26:54] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `autobooks_import_bluebeam_data_product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `autobooks_import_bluebeam_data_account_primary_reseller_name` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-15 10:20:43] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 10:20:43] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `autobooks_import_bluebeam_data_product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `autobooks_import_bluebeam_data_account_primary_reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:06] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:06] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:26] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:26] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:28] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:28] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:29] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:29] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:33] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:33] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:37] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n            [soldto] => Array\n                (\n                    [0] => id\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:37] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `soldto`.`id` AS `soldto_id`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:38] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:38] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:44] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:44] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:48] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:48] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 12:21:51] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 12:21:51] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 13:49:16] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 13:49:16] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 13:49:25] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 13:49:25] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 13:49:28] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 13:49:28] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 13:49:28] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 13:49:28] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 13:49:29] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 13:49:29] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 13:49:42] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-15 13:49:42] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-15 19:38:48] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-15 19:38:48] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `autobooks_import_bluebeam_data_product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `autobooks_import_bluebeam_data_account_primary_reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-16 08:46:59] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-16 08:46:59] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `autobooks_import_bluebeam_data_product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `autobooks_import_bluebeam_data_account_primary_reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-16 09:10:50] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-16 09:10:50] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-17 18:51:41] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [table] => autodesk_accounts\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_table] => autodesk_subscriptions\n                    [right_table] => autodesk_accounts\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n        )\n\n    [selected_columns] => Array\n        (\n            [subs] => Array\n                (\n                    [0] => id\n                    [1] => subscriptionId\n                    [2] => subscriptionReferenceNumber\n                    [3] => quantity\n                    [4] => status\n                    [5] => startDate\n                    [6] => endDate\n                )\n\n            [endcust] => Array\n                (\n                    [0] => id\n                    [1] => account_csn\n                    [2] => name\n                    [3] => first_name\n                    [4] => last_name\n                )\n\n            [lastquote] => Array\n                (\n                    [0] => quote_id\n                    [1] => quote_number\n                    [2] => quoted_date\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[preview_debug] [2025-08-17 18:51:41] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC LIMIT 10\n)\n
[preview_debug] [2025-08-17 20:45:27] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-17 20:45:27] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-17 20:45:49] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                    [12] => created_at\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-17 20:45:49] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country`, `autobooks_import_bluebeam_data`.`created_at` AS `autobooks_import_bluebeam_data_created_at` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-17 20:45:49] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                    [12] => created_at\n                    [13] => updated_at\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-17 20:45:49] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country`, `autobooks_import_bluebeam_data`.`created_at` AS `autobooks_import_bluebeam_data_created_at`, `autobooks_import_bluebeam_data`.`updated_at` AS `autobooks_import_bluebeam_data_updated_at` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-17 20:46:07] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => serial_number\n                    [1] => name\n                    [2] => contract\n                    [3] => product_name\n                    [4] => quantity\n                    [5] => end_date\n                    [6] => account_primary_reseller_name\n                    [7] => order_po_number\n                    [8] => order_shipping_address\n                    [9] => order_shipping_city\n                    [10] => order_shipping_state_province\n                    [11] => order_shipping_country\n                    [12] => created_at\n                    [13] => updated_at\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-17 20:46:07] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country`, `autobooks_import_bluebeam_data`.`created_at` AS `autobooks_import_bluebeam_data_created_at`, `autobooks_import_bluebeam_data`.`updated_at` AS `autobooks_import_bluebeam_data_updated_at` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-17 20:47:37] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autobooks_import_bluebeam_data] => bluebeam\n        )\n\n)\n
[preview_debug] [2025-08-17 20:47:37] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `bluebeam`.`id` AS `autobooks_import_bluebeam_data_id`, `bluebeam`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `bluebeam`.`name` AS `autobooks_import_bluebeam_data_name` FROM `autobooks_import_bluebeam_data` AS `bluebeam` LIMIT 10\n)\n
[preview_debug] [2025-08-17 21:25:25] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_sketchup_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_sketchup_data] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autobooks_import_sketchup_data] => sketchup\n        )\n\n)\n
[preview_debug] [2025-08-17 21:25:25] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `sketchup`.`id` AS `autobooks_import_sketchup_data_id`, `sketchup`.`sold_to_name` AS `autobooks_import_sketchup_data_sold_to_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 10\n)\n
[preview_debug] [2025-08-17 21:55:50] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_sketchup_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_sketchup_data] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autobooks_import_sketchup_data] => sketchup\n        )\n\n)\n
[preview_debug] [2025-08-17 21:55:50] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `sketchup`.`id` AS `autobooks_import_sketchup_data_id`, `sketchup`.`sold_to_name` AS `autobooks_import_sketchup_data_sold_to_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 10\n)\n
[preview_debug] [2025-08-17 22:30:49] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_sketchup_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_sketchup_data] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autobooks_import_sketchup_data] => sketchup\n        )\n\n)\n
[preview_debug] [2025-08-17 22:30:49] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `sketchup`.`id` AS `autobooks_import_sketchup_data_id`, `sketchup`.`sold_to_name` AS `autobooks_import_sketchup_data_sold_to_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 10\n)\n
[preview_debug] [2025-08-17 22:44:11] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_sketchup_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_sketchup_data] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autobooks_import_sketchup_data] => sketchup\n        )\n\n)\n
[preview_debug] [2025-08-17 22:44:11] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `sketchup`.`id` AS `autobooks_import_sketchup_data_id`, `sketchup`.`sold_to_name` AS `autobooks_import_sketchup_data_sold_to_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 10\n)\n
[preview_debug] [2025-08-17 22:52:20] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_sketchup_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [sketchup] => Array\n                (\n                    [0] => sold_to_name\n                    [1] => sold_to_number\n                    [2] => vendor_name\n                    [3] => reseller_number\n                    [4] => reseller_vendor_id\n                    [5] => end_customer_vendor_id\n                    [6] => end_customer_name\n                    [7] => end_customer_address_1\n                    [8] => end_customer_address_2\n                    [9] => end_customer_address_3\n                    [10] => end_customer_city\n                    [11] => end_customer_state\n                    [12] => end_customer_zip_code\n                    [13] => end_customer_country\n                    [14] => end_customer_account_type\n                    [15] => end_customer_contact_name\n                    [16] => end_customer_contact_email\n                    [17] => end_customer_contact_phone\n                    [18] => end_customer_industry_segment\n                    [19] => agreement_program_name\n                    [20] => agreement_number\n                    [21] => agreement_start_date\n                    [22] => agreement_end_date\n                    [23] => agreement_terms\n                    [24] => agreement_type\n                    [25] => agreement_status\n                    [26] => agreement_support_level\n                    [27] => agreement_days_due\n                    [28] => agreement_autorenew\n                    [29] => product_name\n                    [30] => product_family\n                    [31] => product_market_segment\n                    [32] => product_release\n                    [33] => product_type\n                    [34] => product_deployment\n                    [35] => product_sku\n                    [36] => product_sku_description\n                    [37] => product_part\n                    [38] => product_list_price\n                    [39] => product_list_price_currency\n                    [40] => subscription_id\n                    [41] => subscription_serial_number\n                    [42] => subscription_status\n                    [43] => subscription_quantity\n                    [44] => subscription_start_date\n                    [45] => subscription_end_date\n                    [46] => subscription_contact_name\n                    [47] => subscription_contact_email\n                    [48] => subscription_level\n                    [49] => subscription_days_due\n                    [50] => quotation_id\n                    [51] => quotation_type\n                    [52] => quotation_vendor_id\n                    [53] => quotation_deal_registration_number\n                    [54] => quotation_status\n                    [55] => quotation_resellerpo_previous\n                    [56] => quotation_due_date\n                    [57] => flaer_phase\n                    [58] => updated\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n            [autobooks_import_sketchup_data] => sketchup\n        )\n\n)\n
[preview_debug] [2025-08-17 22:52:20] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `sketchup`.`sold_to_name` AS `sketchup_sold_to_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `sketchup_vendor_name`, `sketchup`.`reseller_number` AS `sketchup_reseller_number`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `sketchup_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `sketchup_end_customer_city`, `sketchup`.`end_customer_state` AS `sketchup_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `sketchup_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `sketchup_end_customer_country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `sketchup_end_customer_contact_email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `sketchup_agreement_program_name`, `sketchup`.`agreement_number` AS `sketchup_agreement_number`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `sketchup_agreement_status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `sketchup_product_family`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `sketchup_product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `sketchup_subscription_serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `sketchup_subscription_quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:07] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => unique_hash\n                    [1] => hash_string\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:07] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:08] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => hash_string\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:08] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:09] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => hash_string\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:09] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:11] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:11] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:12] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:12] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:12] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:12] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:13] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                    [6] => intendedUsage_code\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:13] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId`, `products_autodesk_catalog`.`intendedUsage_code` AS `products_autodesk_catalog_intendedUsage_code` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:14] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                    [6] => intendedUsage_code\n                    [7] => intendedUsage_description\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:14] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId`, `products_autodesk_catalog`.`intendedUsage_code` AS `products_autodesk_catalog_intendedUsage_code`, `products_autodesk_catalog`.`intendedUsage_description` AS `products_autodesk_catalog_intendedUsage_description` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:16] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                    [6] => intendedUsage_code\n                    [7] => intendedUsage_description\n                    [8] => accessModel_code\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:16] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId`, `products_autodesk_catalog`.`intendedUsage_code` AS `products_autodesk_catalog_intendedUsage_code`, `products_autodesk_catalog`.`intendedUsage_description` AS `products_autodesk_catalog_intendedUsage_description`, `products_autodesk_catalog`.`accessModel_code` AS `products_autodesk_catalog_accessModel_code` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:18] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                    [6] => intendedUsage_code\n                    [7] => intendedUsage_description\n                    [8] => accessModel_code\n                    [9] => accessModel_description\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:18] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId`, `products_autodesk_catalog`.`intendedUsage_code` AS `products_autodesk_catalog_intendedUsage_code`, `products_autodesk_catalog`.`intendedUsage_description` AS `products_autodesk_catalog_intendedUsage_description`, `products_autodesk_catalog`.`accessModel_code` AS `products_autodesk_catalog_accessModel_code`, `products_autodesk_catalog`.`accessModel_description` AS `products_autodesk_catalog_accessModel_description` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:19] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                    [6] => intendedUsage_code\n                    [7] => intendedUsage_description\n                    [8] => accessModel_code\n                    [9] => accessModel_description\n                    [10] => servicePlan_code\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:19] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId`, `products_autodesk_catalog`.`intendedUsage_code` AS `products_autodesk_catalog_intendedUsage_code`, `products_autodesk_catalog`.`intendedUsage_description` AS `products_autodesk_catalog_intendedUsage_description`, `products_autodesk_catalog`.`accessModel_code` AS `products_autodesk_catalog_accessModel_code`, `products_autodesk_catalog`.`accessModel_description` AS `products_autodesk_catalog_accessModel_description`, `products_autodesk_catalog`.`servicePlan_code` AS `products_autodesk_catalog_servicePlan_code` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:20] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                    [6] => intendedUsage_code\n                    [7] => intendedUsage_description\n                    [8] => accessModel_code\n                    [9] => accessModel_description\n                    [10] => servicePlan_code\n                    [11] => servicePlan_description\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:20] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId`, `products_autodesk_catalog`.`intendedUsage_code` AS `products_autodesk_catalog_intendedUsage_code`, `products_autodesk_catalog`.`intendedUsage_description` AS `products_autodesk_catalog_intendedUsage_description`, `products_autodesk_catalog`.`accessModel_code` AS `products_autodesk_catalog_accessModel_code`, `products_autodesk_catalog`.`accessModel_description` AS `products_autodesk_catalog_accessModel_description`, `products_autodesk_catalog`.`servicePlan_code` AS `products_autodesk_catalog_servicePlan_code`, `products_autodesk_catalog`.`servicePlan_description` AS `products_autodesk_catalog_servicePlan_description` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:11:21] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                    [2] => hash_string\n                    [3] => offeringName\n                    [4] => offeringCode\n                    [5] => offeringId\n                    [6] => intendedUsage_code\n                    [7] => intendedUsage_description\n                    [8] => accessModel_code\n                    [9] => accessModel_description\n                    [10] => servicePlan_code\n                    [11] => servicePlan_description\n                    [12] => connectivity_code\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:11:21] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash`, `products_autodesk_catalog`.`hash_string` AS `products_autodesk_catalog_hash_string`, `products_autodesk_catalog`.`offeringName` AS `products_autodesk_catalog_offeringName`, `products_autodesk_catalog`.`offeringCode` AS `products_autodesk_catalog_offeringCode`, `products_autodesk_catalog`.`offeringId` AS `products_autodesk_catalog_offeringId`, `products_autodesk_catalog`.`intendedUsage_code` AS `products_autodesk_catalog_intendedUsage_code`, `products_autodesk_catalog`.`intendedUsage_description` AS `products_autodesk_catalog_intendedUsage_description`, `products_autodesk_catalog`.`accessModel_code` AS `products_autodesk_catalog_accessModel_code`, `products_autodesk_catalog`.`accessModel_description` AS `products_autodesk_catalog_accessModel_description`, `products_autodesk_catalog`.`servicePlan_code` AS `products_autodesk_catalog_servicePlan_code`, `products_autodesk_catalog`.`servicePlan_description` AS `products_autodesk_catalog_servicePlan_description`, `products_autodesk_catalog`.`connectivity_code` AS `products_autodesk_catalog_connectivity_code` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:16:33] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:16:33] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:16:34] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:16:34] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:16:35] [data_sources.api.php:1422] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:16:35] [data_sources.api.php:1439] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:30:00] [data_sources.api.php:1350] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                    [1] => unique_hash\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:30:00] [data_sources.api.php:1367] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id`, `products_autodesk_catalog`.`unique_hash` AS `products_autodesk_catalog_unique_hash` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:30:02] [data_sources.api.php:1350] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:30:02] [data_sources.api.php:1367] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:30:02] [data_sources.api.php:1350] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [products_autodesk_catalog] => Array\n                (\n                    [0] => id\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:30:02] [data_sources.api.php:1367] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `products_autodesk_catalog`.`id` AS `products_autodesk_catalog_id` FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:30:19] [data_sources.api.php:1350] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:30:19] [data_sources.api.php:1367] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT * FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-18 08:30:50] [data_sources.api.php:1350] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => products_autodesk_catalog\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-18 08:30:50] [data_sources.api.php:1367] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT * FROM `products_autodesk_catalog` LIMIT 10\n)\n
[preview_debug] [2025-08-19 10:18:17] [data_sources.api.php:1350] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_sketchup_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n        )\n\n    [table_aliases] => Array\n        (\n            [autobooks_import_sketchup_data] => sketchup\n        )\n\n)\n
[preview_debug] [2025-08-19 10:18:17] [data_sources.api.php:1367] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT * FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 10\n)\n
