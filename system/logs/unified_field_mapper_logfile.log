[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:460] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:71]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:397] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:01:04] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 285 days remaining
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> endcust_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> end_customer_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> endcust_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> end_customer_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_city -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_state -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_country -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:54] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> endcust_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> end_customer_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> endcust_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> end_customer_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_city -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_state -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_country -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 14:47:56] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> endcust_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> end_customer_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> endcust_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> end_customer_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_city -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_state -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_country -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-18 15:36:34] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> endcust_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> end_customer_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> endcust_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> end_customer_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_city -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_state -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_country -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 07:40:03] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> endcust_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> end_customer_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> endcust_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> end_customer_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_city -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_state -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_country -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 08:56:30] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:547] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped sold_to_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: vendor_name -> company_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: vendor_name -> endcust_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: vendor_name -> end_customer_name (score: 74) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped vendor_name -> company_name (priority: 2, score: 74) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped reseller_number -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> partner_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: reseller_vendor_id -> account_primary_reseller_name (score: 63.3) loses to existing reseller_number (score: 63.3)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped reseller_vendor_id -> reseller_name (priority: 3, score: 63.3) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> endcust_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_vendor_id -> end_customer_name (score: 65.8) loses to existing sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_vendor_id -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> endcust_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:115] Conflict: end_customer_name -> end_customer_name (score: 77) replaces sold_to_name (score: 74)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_name -> company_name (priority: 2, score: 77) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_1 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_address_1 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_2 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_address_2 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_address_3 -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_address_3 -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_city -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_city -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_state -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_state -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_zip_code -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_zip_code -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:113] Conflict: end_customer_country -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:43] [unified_field_mapper.class.php:137] Mapped end_customer_country -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_account_type -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped end_customer_account_type -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_name -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped end_customer_contact_name -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped end_customer_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_contact_phone -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped end_customer_contact_phone -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> endcust_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: end_customer_industry_segment -> end_customer_name (score: 65.8) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped end_customer_industry_segment -> company_name (priority: 2, score: 65.8) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped agreement_program_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped agreement_number -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped agreement_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped agreement_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped agreement_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_name -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_name -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_name -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_family -> product_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_family -> subs_offeringName (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_family -> agreement_program_name (score: 79) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_family -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_market_segment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_market_segment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_release -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_release -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_release -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_release -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_type -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_type -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_type -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_type -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_deployment -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_deployment -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_deployment -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_deployment -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_sku -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_sku -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_sku -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_sku -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_sku_description -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_sku_description -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_part -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_part -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_part -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_part -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_list_price -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_list_price -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_list_price -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_list_price -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> product_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> subs_offeringName (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: product_list_price_currency -> agreement_program_name (score: 70.2) loses to existing agreement_program_name (score: 79)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped product_list_price_currency -> product_name (priority: 1, score: 70.2) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_reference\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subs_subscriptionReferenceNumber\n (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_id -> subscription_id (score: 85) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_id -> subscription_reference (priority: 0, score: 85) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_reference\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subs_subscriptionReferenceNumber\n (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_serial_number -> subscription_id (score: 79) loses to existing agreement_number (score: 85)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_serial_number -> subscription_reference (priority: 0, score: 79) -> subscription_reference\n, subs_subscriptionReferenceNumber\n, subscription_id
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_status -> status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subs_status\n (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_status -> subscription_status (score: 58) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_status -> status (priority: 10, score: 58) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> start_date\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> subs_startDate\n (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_start_date -> agreement_start_date (score: 58) loses to existing agreement_start_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_start_date -> start_date (priority: 10, score: 58) -> start_date\n, subs_startDate\n, agreement_start_date
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> end_date\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> subs_endDate\n (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_end_date -> agreement_end_date (score: 58) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> company_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> endcust_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_contact_name -> end_customer_name (score: 63.4) loses to existing end_customer_name (score: 77)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_contact_name -> company_name (priority: 2, score: 63.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> email_address (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> endcust_primary_admin_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: subscription_contact_email -> end_customer_contact_email (score: 82) loses to existing end_customer_contact_email (score: 82)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped subscription_contact_email -> email (priority: 1, score: 82) -> email_address, endcust_primary_admin_email, end_customer_contact_email
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: quotation_status -> status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subs_status\n (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: quotation_status -> subscription_status (score: 52) loses to existing agreement_status (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped quotation_status -> status (priority: 10, score: 52) -> status\n, subs_status\n, subscription_status
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> end_date\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> subs_endDate\n (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:113] Conflict: quotation_due_date -> agreement_end_date (score: 52) loses to existing agreement_end_date (score: 58)
[unified_field_mapper] [2025-08-19 09:28:44] [unified_field_mapper.class.php:137] Mapped quotation_due_date -> end_date (priority: 10, score: 52) -> end_date\n, subs_endDate\n, agreement_end_date
