[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 14:10:38
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 14:10:38
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:36]  Provided signature: sha256=7e16fa04b29fa44944f01f2ac0d7284076853609320c3ca4f2a19d936241a29e
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:37]  Calculated signature: sha256=7e16fa04b29fa44944f01f2ac0d7284076853609320c3ca4f2a19d936241a29e
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 50db567c95be3d5a\n    [X-B3-<PERSON><PERSON>] => 689dee5c726c665810cf147200935b70\n    [B3] => 689dee5c726c665810cf147200935b70-50db567c95be3d5a-1\n    [Traceparent] => 00-689dee5c726c665810cf147200935b70-50db567c95be3d5a-01\n    [X-Amzn-Trace-Id] => Root=1-689dee5c-726c665810cf147200935b70;Parent=50db567c95be3d5a;Sampled=1\n    [X-Adsk-Signature] => sha256=7e16fa04b29fa44944f01f2ac0d7284076853609320c3ca4f2a19d936241a29e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d5e728bf-256b-464f-8471-eb34b687ef2c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"d5e728bf-256b-464f-8471-eb34b687ef2c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59766978865168","status":"Active","quantity":1,"endDate":"2026-08-16","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-14T13:35:33.000+0000"},"publishedAt":"2025-08-14T14:10:36.000Z","csn":"5103159758"}
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:36]  Provided signature: sha256=22840dffb41cba18418b0a51cf02a0e287ded0848e54ac1b0d43d2ec0da5d465
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:37]  Calculated signature: sha256=7e16fa04b29fa44944f01f2ac0d7284076853609320c3ca4f2a19d936241a29e
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1eb50215a40aa74d\n    [X-B3-Traceid] => 689dee5c726c665810cf147200935b70\n    [B3] => 689dee5c726c665810cf147200935b70-1eb50215a40aa74d-1\n    [Traceparent] => 00-689dee5c726c665810cf147200935b70-1eb50215a40aa74d-01\n    [X-Amzn-Trace-Id] => Root=1-689dee5c-726c665810cf147200935b70;Parent=1eb50215a40aa74d;Sampled=1\n    [X-Adsk-Signature] => sha256=22840dffb41cba18418b0a51cf02a0e287ded0848e54ac1b0d43d2ec0da5d465\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d5e728bf-256b-464f-8471-eb34b687ef2c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 14:10:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"d5e728bf-256b-464f-8471-eb34b687ef2c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59766978865168","status":"Active","quantity":1,"endDate":"2026-08-16","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-14T13:35:33.000+0000"},"publishedAt":"2025-08-14T14:10:36.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:41:38
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:36]  Provided signature: sha256=0ba5ef09f9ed6c09197a8ec244cd98fc7967f40710a6aa3f044ae624cf5a3f89
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:37]  Calculated signature: sha256=da5e645bea4841a4216bc90fd57461d173bb3b02c3473acf3794c74ab2bba9c9
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 90a64ebeed63240d\n    [X-B3-Traceid] => 689e03afba27a6360b9e1279f0116a58\n    [B3] => 689e03afba27a6360b9e1279f0116a58-90a64ebeed63240d-1\n    [Traceparent] => 00-689e03afba27a6360b9e1279f0116a58-90a64ebeed63240d-01\n    [X-Amzn-Trace-Id] => Root=1-689e03af-ba27a6360b9e1279f0116a58;Parent=90a64ebeed63240d;Sampled=1\n    [X-Adsk-Signature] => sha256=0ba5ef09f9ed6c09197a8ec244cd98fc7967f40710a6aa3f044ae624cf5a3f89\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 687f4d53-4da9-4337-82ff-e93e223e729c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"687f4d53-4da9-4337-82ff-e93e223e729c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007484","transactionId":"76a084a2-65fc-57f3-a115-e5ba601e7437","quoteStatus":"Draft","message":"Quote# Q-1007484 status changed to Draft.","modifiedAt":"2025-08-14T15:41:35.610Z"},"publishedAt":"2025-08-14T15:41:36.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:41:38
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:36]  Provided signature: sha256=da5e645bea4841a4216bc90fd57461d173bb3b02c3473acf3794c74ab2bba9c9
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:37]  Calculated signature: sha256=da5e645bea4841a4216bc90fd57461d173bb3b02c3473acf3794c74ab2bba9c9
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 740892aba0bf57d7\n    [X-B3-Traceid] => 689e03afba27a6360b9e1279f0116a58\n    [B3] => 689e03afba27a6360b9e1279f0116a58-740892aba0bf57d7-1\n    [Traceparent] => 00-689e03afba27a6360b9e1279f0116a58-740892aba0bf57d7-01\n    [X-Amzn-Trace-Id] => Root=1-689e03af-ba27a6360b9e1279f0116a58;Parent=740892aba0bf57d7;Sampled=1\n    [X-Adsk-Signature] => sha256=da5e645bea4841a4216bc90fd57461d173bb3b02c3473acf3794c74ab2bba9c9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 687f4d53-4da9-4337-82ff-e93e223e729c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:41:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"687f4d53-4da9-4337-82ff-e93e223e729c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007484","transactionId":"76a084a2-65fc-57f3-a115-e5ba601e7437","quoteStatus":"Draft","message":"Quote# Q-1007484 status changed to Draft.","modifiedAt":"2025-08-14T15:41:35.610Z"},"publishedAt":"2025-08-14T15:41:36.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:41:42
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:36]  Provided signature: sha256=fceca943369530c2ea9b8e849f3da76de9cd7ee5e6a20944e797abad67003a20
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:37]  Calculated signature: sha256=fceca943369530c2ea9b8e849f3da76de9cd7ee5e6a20944e797abad67003a20
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9eef7473874de0e3\n    [X-B3-Traceid] => 689e03b1032c44a2e1a492fd04621f22\n    [B3] => 689e03b1032c44a2e1a492fd04621f22-9eef7473874de0e3-1\n    [Traceparent] => 00-689e03b1032c44a2e1a492fd04621f22-9eef7473874de0e3-01\n    [X-Amzn-Trace-Id] => Root=1-689e03b1-032c44a2e1a492fd04621f22;Parent=9eef7473874de0e3;Sampled=1\n    [X-Adsk-Signature] => sha256=fceca943369530c2ea9b8e849f3da76de9cd7ee5e6a20944e797abad67003a20\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2fdb127c-3fde-4f21-9466-2036b964fee5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"2fdb127c-3fde-4f21-9466-2036b964fee5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007485","transactionId":"fbf37246-eb4a-5e2d-bee6-135eeea00b12","quoteStatus":"Draft","message":"Quote# Q-1007485 status changed to Draft.","modifiedAt":"2025-08-14T15:41:37.439Z"},"publishedAt":"2025-08-14T15:41:38.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:41:42
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:36]  Provided signature: sha256=128acf0500174810176b64628dfeb7e7de099f3fca9905a9791cea0429e8b7d9
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:37]  Calculated signature: sha256=fceca943369530c2ea9b8e849f3da76de9cd7ee5e6a20944e797abad67003a20
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 329456233b881068\n    [X-B3-Traceid] => 689e03b1032c44a2e1a492fd04621f22\n    [B3] => 689e03b1032c44a2e1a492fd04621f22-329456233b881068-1\n    [Traceparent] => 00-689e03b1032c44a2e1a492fd04621f22-329456233b881068-01\n    [X-Amzn-Trace-Id] => Root=1-689e03b1-032c44a2e1a492fd04621f22;Parent=329456233b881068;Sampled=1\n    [X-Adsk-Signature] => sha256=128acf0500174810176b64628dfeb7e7de099f3fca9905a9791cea0429e8b7d9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2fdb127c-3fde-4f21-9466-2036b964fee5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:41:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"2fdb127c-3fde-4f21-9466-2036b964fee5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007485","transactionId":"fbf37246-eb4a-5e2d-bee6-135eeea00b12","quoteStatus":"Draft","message":"Quote# Q-1007485 status changed to Draft.","modifiedAt":"2025-08-14T15:41:37.439Z"},"publishedAt":"2025-08-14T15:41:38.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:42:14
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:36]  Provided signature: sha256=b46cce468f3f28556903097acbe0d14e8c45090d65a871dc8033e7ed9fea69a1
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:37]  Calculated signature: sha256=b46cce468f3f28556903097acbe0d14e8c45090d65a871dc8033e7ed9fea69a1
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 49629e0d721f9439\n    [X-B3-Traceid] => 689e03d3771205821d3d8d75cd15e17b\n    [B3] => 689e03d3771205821d3d8d75cd15e17b-49629e0d721f9439-1\n    [Traceparent] => 00-689e03d3771205821d3d8d75cd15e17b-49629e0d721f9439-01\n    [X-Amzn-Trace-Id] => Root=1-689e03d3-771205821d3d8d75cd15e17b;Parent=49629e0d721f9439;Sampled=1\n    [X-Adsk-Signature] => sha256=b46cce468f3f28556903097acbe0d14e8c45090d65a871dc8033e7ed9fea69a1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 81ace513-75c2-4c4f-ac1b-f77c4b77bc27\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"81ace513-75c2-4c4f-ac1b-f77c4b77bc27","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007484","transactionId":"76a084a2-65fc-57f3-a115-e5ba601e7437","quoteStatus":"Quoted","message":"Quote# Q-1007484 status changed to Quoted.","modifiedAt":"2025-08-14T15:42:11.166Z"},"publishedAt":"2025-08-14T15:42:11.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:42:14
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:36]  Provided signature: sha256=3ae5e2c1c10a78968339dec1286bd6177f3834ae26f62a3c739c482fbdcf3117
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:37]  Calculated signature: sha256=b46cce468f3f28556903097acbe0d14e8c45090d65a871dc8033e7ed9fea69a1
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => aa7136f174fa8860\n    [X-B3-Traceid] => 689e03d3771205821d3d8d75cd15e17b\n    [B3] => 689e03d3771205821d3d8d75cd15e17b-aa7136f174fa8860-1\n    [Traceparent] => 00-689e03d3771205821d3d8d75cd15e17b-aa7136f174fa8860-01\n    [X-Amzn-Trace-Id] => Root=1-689e03d3-771205821d3d8d75cd15e17b;Parent=aa7136f174fa8860;Sampled=1\n    [X-Adsk-Signature] => sha256=3ae5e2c1c10a78968339dec1286bd6177f3834ae26f62a3c739c482fbdcf3117\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 81ace513-75c2-4c4f-ac1b-f77c4b77bc27\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:42:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"81ace513-75c2-4c4f-ac1b-f77c4b77bc27","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007484","transactionId":"76a084a2-65fc-57f3-a115-e5ba601e7437","quoteStatus":"Quoted","message":"Quote# Q-1007484 status changed to Quoted.","modifiedAt":"2025-08-14T15:42:11.166Z"},"publishedAt":"2025-08-14T15:42:11.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:42:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:42:34
[webhook] [2025-08-14 15:42:34] [adwsapi_v2.php:36]  Provided signature: sha256=6d00d1a04e44b4f6f32f9fdd1059546ac9a97124d643b5b663208dcb74d947c5
[webhook] [2025-08-14 15:42:34] [adwsapi_v2.php:37]  Calculated signature: sha256=6d00d1a04e44b4f6f32f9fdd1059546ac9a97124d643b5b663208dcb74d947c5
[webhook] [2025-08-14 15:42:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b7cf25077854fe0b\n    [X-B3-Traceid] => 689e03e702f7af9e8b77c0b61d195faf\n    [B3] => 689e03e702f7af9e8b77c0b61d195faf-b7cf25077854fe0b-1\n    [Traceparent] => 00-689e03e702f7af9e8b77c0b61d195faf-b7cf25077854fe0b-01\n    [X-Amzn-Trace-Id] => Root=1-689e03e7-02f7af9e8b77c0b61d195faf;Parent=b7cf25077854fe0b;Sampled=1\n    [X-Adsk-Signature] => sha256=6d00d1a04e44b4f6f32f9fdd1059546ac9a97124d643b5b663208dcb74d947c5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 896e2739-d970-4be5-aa84-a0b6a339189b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:42:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"896e2739-d970-4be5-aa84-a0b6a339189b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007485","transactionId":"fbf37246-eb4a-5e2d-bee6-135eeea00b12","quoteStatus":"Quoted","message":"Quote# Q-1007485 status changed to Quoted.","modifiedAt":"2025-08-14T15:42:31.776Z"},"publishedAt":"2025-08-14T15:42:32.000Z","csn":"5103159758"}
[webhook] [2025-08-14 15:42:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 15:42:35
[webhook] [2025-08-14 15:42:35] [adwsapi_v2.php:36]  Provided signature: sha256=1cb767307ae6c1152940e7a5581b439bfaed6c9c8492b53665ecdeacf3aa944e
[webhook] [2025-08-14 15:42:35] [adwsapi_v2.php:37]  Calculated signature: sha256=6d00d1a04e44b4f6f32f9fdd1059546ac9a97124d643b5b663208dcb74d947c5
[webhook] [2025-08-14 15:42:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 03a962e56e5ec37c\n    [X-B3-Traceid] => 689e03e702f7af9e8b77c0b61d195faf\n    [B3] => 689e03e702f7af9e8b77c0b61d195faf-03a962e56e5ec37c-1\n    [Traceparent] => 00-689e03e702f7af9e8b77c0b61d195faf-03a962e56e5ec37c-01\n    [X-Amzn-Trace-Id] => Root=1-689e03e7-02f7af9e8b77c0b61d195faf;Parent=03a962e56e5ec37c;Sampled=1\n    [X-Adsk-Signature] => sha256=1cb767307ae6c1152940e7a5581b439bfaed6c9c8492b53665ecdeacf3aa944e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 896e2739-d970-4be5-aa84-a0b6a339189b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 15:42:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"896e2739-d970-4be5-aa84-a0b6a339189b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1007485","transactionId":"fbf37246-eb4a-5e2d-bee6-135eeea00b12","quoteStatus":"Quoted","message":"Quote# Q-1007485 status changed to Quoted.","modifiedAt":"2025-08-14T15:42:31.776Z"},"publishedAt":"2025-08-14T15:42:32.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:01:18
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:36]  Provided signature: sha256=cc76624ec2ca2265f20c352b5fd24e7143100db3be2c05d02e87ec799269ddfa
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:37]  Calculated signature: sha256=07a44ac2a372775bfbb21ca4eb471149f84af4f27350f7268dc80c5533a3195d
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 101e93a68224f5c5\n    [X-B3-Traceid] => 689e084c2fbb043631795d065d6e6d3a\n    [B3] => 689e084c2fbb043631795d065d6e6d3a-101e93a68224f5c5-1\n    [Traceparent] => 00-689e084c2fbb043631795d065d6e6d3a-101e93a68224f5c5-01\n    [X-Amzn-Trace-Id] => Root=1-689e084c-2fbb043631795d065d6e6d3a;Parent=101e93a68224f5c5;Sampled=1\n    [X-Adsk-Signature] => sha256=cc76624ec2ca2265f20c352b5fd24e7143100db3be2c05d02e87ec799269ddfa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187276351-59792274819899\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187276351-59792274819899","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59792274819899","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:01:16.351Z"},"publishedAt":"2025-08-14T16:01:16.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:01:18
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:36]  Provided signature: sha256=07a44ac2a372775bfbb21ca4eb471149f84af4f27350f7268dc80c5533a3195d
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:37]  Calculated signature: sha256=07a44ac2a372775bfbb21ca4eb471149f84af4f27350f7268dc80c5533a3195d
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 908517ee43b32d7c\n    [X-B3-Traceid] => 689e084c2fbb043631795d065d6e6d3a\n    [B3] => 689e084c2fbb043631795d065d6e6d3a-908517ee43b32d7c-1\n    [Traceparent] => 00-689e084c2fbb043631795d065d6e6d3a-908517ee43b32d7c-01\n    [X-Amzn-Trace-Id] => Root=1-689e084c-2fbb043631795d065d6e6d3a;Parent=908517ee43b32d7c;Sampled=1\n    [X-Adsk-Signature] => sha256=07a44ac2a372775bfbb21ca4eb471149f84af4f27350f7268dc80c5533a3195d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187276351-59792274819899\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:01:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187276351-59792274819899","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59792274819899","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:01:16.351Z"},"publishedAt":"2025-08-14T16:01:16.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:05:07
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:36]  Provided signature: sha256=713c8e56723bf7c41fb7745de8400ffc4b47f6adee328d1b9c0c3486bb35faf7
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:37]  Calculated signature: sha256=713c8e56723bf7c41fb7745de8400ffc4b47f6adee328d1b9c0c3486bb35faf7
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 251ffcdad879b995\n    [X-B3-Traceid] => 689e0930679a62693ded5a7d2a852baa\n    [B3] => 689e0930679a62693ded5a7d2a852baa-251ffcdad879b995-1\n    [Traceparent] => 00-689e0930679a62693ded5a7d2a852baa-251ffcdad879b995-01\n    [X-Amzn-Trace-Id] => Root=1-689e0930-679a62693ded5a7d2a852baa;Parent=251ffcdad879b995;Sampled=1\n    [X-Adsk-Signature] => sha256=713c8e56723bf7c41fb7745de8400ffc4b47f6adee328d1b9c0c3486bb35faf7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187504987-66264904812166\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187504987-66264904812166","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:05:04.987Z"},"publishedAt":"2025-08-14T16:05:05.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:05:07
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:36]  Provided signature: sha256=63e32995a5256e60253b3b9edcc1c06df1cdb8c9de55c04f74979356429a0d2a
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:37]  Calculated signature: sha256=713c8e56723bf7c41fb7745de8400ffc4b47f6adee328d1b9c0c3486bb35faf7
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 33af507635699799\n    [X-B3-Traceid] => 689e0930679a62693ded5a7d2a852baa\n    [B3] => 689e0930679a62693ded5a7d2a852baa-33af507635699799-1\n    [Traceparent] => 00-689e0930679a62693ded5a7d2a852baa-33af507635699799-01\n    [X-Amzn-Trace-Id] => Root=1-689e0930-679a62693ded5a7d2a852baa;Parent=33af507635699799;Sampled=1\n    [X-Adsk-Signature] => sha256=63e32995a5256e60253b3b9edcc1c06df1cdb8c9de55c04f74979356429a0d2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187504987-66264904812166\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:05:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187504987-66264904812166","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:05:04.987Z"},"publishedAt":"2025-08-14T16:05:05.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:05:35
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:36]  Provided signature: sha256=9ce091f846ab02b7605053abafd38258900454ec0427b4c9d1fcc2c1a8a12a60
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:37]  Calculated signature: sha256=b8b4dcbcc5d902e00f6701d2b75bdf004524f4021245669b6c814262aa4c9384
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 28a2e8bc6e931837\n    [X-B3-Traceid] => 689e094c5b345f7978eae44f24f71c79\n    [B3] => 689e094c5b345f7978eae44f24f71c79-28a2e8bc6e931837-1\n    [Traceparent] => 00-689e094c5b345f7978eae44f24f71c79-28a2e8bc6e931837-01\n    [X-Amzn-Trace-Id] => Root=1-689e094c-5b345f7978eae44f24f71c79;Parent=28a2e8bc6e931837;Sampled=1\n    [X-Adsk-Signature] => sha256=9ce091f846ab02b7605053abafd38258900454ec0427b4c9d1fcc2c1a8a12a60\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187532865-63247464085385\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187532865-63247464085385","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63247464085385","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:05:32.865Z"},"publishedAt":"2025-08-14T16:05:32.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:05:35
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:36]  Provided signature: sha256=b8b4dcbcc5d902e00f6701d2b75bdf004524f4021245669b6c814262aa4c9384
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:37]  Calculated signature: sha256=b8b4dcbcc5d902e00f6701d2b75bdf004524f4021245669b6c814262aa4c9384
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ac0a409983706edd\n    [X-B3-Traceid] => 689e094c5b345f7978eae44f24f71c79\n    [B3] => 689e094c5b345f7978eae44f24f71c79-ac0a409983706edd-1\n    [Traceparent] => 00-689e094c5b345f7978eae44f24f71c79-ac0a409983706edd-01\n    [X-Amzn-Trace-Id] => Root=1-689e094c-5b345f7978eae44f24f71c79;Parent=ac0a409983706edd;Sampled=1\n    [X-Adsk-Signature] => sha256=b8b4dcbcc5d902e00f6701d2b75bdf004524f4021245669b6c814262aa4c9384\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187532865-63247464085385\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:05:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187532865-63247464085385","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63247464085385","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:05:32.865Z"},"publishedAt":"2025-08-14T16:05:32.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:10:39
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:36]  Provided signature: sha256=d9a52c2b47c9e889459384c10804fee30fc535f019a782cf92b765e7554e2618
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:37]  Calculated signature: sha256=d9a52c2b47c9e889459384c10804fee30fc535f019a782cf92b765e7554e2618
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d74afdf92a66d7da\n    [X-B3-Traceid] => 689e0a7c1918f8d51b85ad323dbd790d\n    [B3] => 689e0a7c1918f8d51b85ad323dbd790d-d74afdf92a66d7da-1\n    [Traceparent] => 00-689e0a7c1918f8d51b85ad323dbd790d-d74afdf92a66d7da-01\n    [X-Amzn-Trace-Id] => Root=1-689e0a7c-1918f8d51b85ad323dbd790d;Parent=d74afdf92a66d7da;Sampled=1\n    [X-Adsk-Signature] => sha256=d9a52c2b47c9e889459384c10804fee30fc535f019a782cf92b765e7554e2618\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187836995-63361092037641\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187836995-63361092037641","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63361092037641","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:10:36.995Z"},"publishedAt":"2025-08-14T16:10:37.000Z","csn":"5103159758"}
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 16:10:39
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:36]  Provided signature: sha256=bc55053361987eb1a582182a27075c6167d5e7dbf52e534ec92e06fdc81add40
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:37]  Calculated signature: sha256=d9a52c2b47c9e889459384c10804fee30fc535f019a782cf92b765e7554e2618
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4f2fe9bda31aeb66\n    [X-B3-Traceid] => 689e0a7c1918f8d51b85ad323dbd790d\n    [B3] => 689e0a7c1918f8d51b85ad323dbd790d-4f2fe9bda31aeb66-1\n    [Traceparent] => 00-689e0a7c1918f8d51b85ad323dbd790d-4f2fe9bda31aeb66-01\n    [X-Amzn-Trace-Id] => Root=1-689e0a7c-1918f8d51b85ad323dbd790d;Parent=4f2fe9bda31aeb66;Sampled=1\n    [X-Adsk-Signature] => sha256=bc55053361987eb1a582182a27075c6167d5e7dbf52e534ec92e06fdc81add40\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755187836995-63361092037641\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 16:10:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755187836995-63361092037641","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63361092037641","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T16:10:36.995Z"},"publishedAt":"2025-08-14T16:10:37.000Z","csn":"5103159758"}
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 19:37:46
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:36]  Provided signature: sha256=c7821cdc365db598395c69d61c5ce9fe7f8f91ef57803702a0771e5efe02d151
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:37]  Calculated signature: sha256=c7821cdc365db598395c69d61c5ce9fe7f8f91ef57803702a0771e5efe02d151
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 73ca969d03016a44\n    [X-B3-Traceid] => 689e3b087ebd79511cea387268cc6892\n    [B3] => 689e3b087ebd79511cea387268cc6892-73ca969d03016a44-1\n    [Traceparent] => 00-689e3b087ebd79511cea387268cc6892-73ca969d03016a44-01\n    [X-Amzn-Trace-Id] => Root=1-689e3b08-7ebd79511cea387268cc6892;Parent=73ca969d03016a44;Sampled=1\n    [X-Adsk-Signature] => sha256=c7821cdc365db598395c69d61c5ce9fe7f8f91ef57803702a0771e5efe02d151\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bbe0f2cd-8b09-4322-9ac6-c8bc7d8656ee\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"bbe0f2cd-8b09-4322-9ac6-c8bc7d8656ee","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59766978865168","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-14T19:17:38.000+0000"},"publishedAt":"2025-08-14T19:37:44.000Z","csn":"5103159758"}
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 19:37:46
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:36]  Provided signature: sha256=faf8a0fd1bab7011792cd85617b16d30a6165e0db0cfe2d85b93a78836cb7dbb
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:37]  Calculated signature: sha256=c7821cdc365db598395c69d61c5ce9fe7f8f91ef57803702a0771e5efe02d151
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 267721419cf70049\n    [X-B3-Traceid] => 689e3b087ebd79511cea387268cc6892\n    [B3] => 689e3b087ebd79511cea387268cc6892-267721419cf70049-1\n    [Traceparent] => 00-689e3b087ebd79511cea387268cc6892-267721419cf70049-01\n    [X-Amzn-Trace-Id] => Root=1-689e3b08-7ebd79511cea387268cc6892;Parent=267721419cf70049;Sampled=1\n    [X-Adsk-Signature] => sha256=faf8a0fd1bab7011792cd85617b16d30a6165e0db0cfe2d85b93a78836cb7dbb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bbe0f2cd-8b09-4322-9ac6-c8bc7d8656ee\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 19:37:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"bbe0f2cd-8b09-4322-9ac6-c8bc7d8656ee","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59766978865168","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-14T19:17:38.000+0000"},"publishedAt":"2025-08-14T19:37:44.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:07:55
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:36]  Provided signature: sha256=d2d2ad49c8eafdc468a4b788434717b6e63ca9daa45b4a7ed4879204c1187c95
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:37]  Calculated signature: sha256=d2d2ad49c8eafdc468a4b788434717b6e63ca9daa45b4a7ed4879204c1187c95
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0a4d43698b9c330d\n    [X-B3-Traceid] => 689e42170a960f77227105ca4f6d0e8f\n    [B3] => 689e42170a960f77227105ca4f6d0e8f-0a4d43698b9c330d-1\n    [Traceparent] => 00-689e42170a960f77227105ca4f6d0e8f-0a4d43698b9c330d-01\n    [X-Amzn-Trace-Id] => Root=1-689e4217-0a960f77227105ca4f6d0e8f;Parent=0a4d43698b9c330d;Sampled=1\n    [X-Adsk-Signature] => sha256=d2d2ad49c8eafdc468a4b788434717b6e63ca9daa45b4a7ed4879204c1187c95\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202071248-59766978865168\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202071248-59766978865168","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59766978865168","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:07:51.248Z"},"publishedAt":"2025-08-14T20:07:51.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:07:55
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:36]  Provided signature: sha256=a821d1c04bcd0a6847b545cee505efbc53e59c2fe8a92f2c7d6abfdb2608cb17
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:37]  Calculated signature: sha256=d2d2ad49c8eafdc468a4b788434717b6e63ca9daa45b4a7ed4879204c1187c95
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 24ea7cfd0e7003cd\n    [X-B3-Traceid] => 689e42170a960f77227105ca4f6d0e8f\n    [B3] => 689e42170a960f77227105ca4f6d0e8f-24ea7cfd0e7003cd-1\n    [Traceparent] => 00-689e42170a960f77227105ca4f6d0e8f-24ea7cfd0e7003cd-01\n    [X-Amzn-Trace-Id] => Root=1-689e4217-0a960f77227105ca4f6d0e8f;Parent=24ea7cfd0e7003cd;Sampled=1\n    [X-Adsk-Signature] => sha256=a821d1c04bcd0a6847b545cee505efbc53e59c2fe8a92f2c7d6abfdb2608cb17\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202071248-59766978865168\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:07:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202071248-59766978865168","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59766978865168","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:07:51.248Z"},"publishedAt":"2025-08-14T20:07:51.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:17:14
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:36]  Provided signature: sha256=3b65d791e503b04e1cf0fca3824f85145255c63c655c5e444e00b00d0fb1d137
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:37]  Calculated signature: sha256=f9906b5929bad548b9e6977cf933e9b0b4e3e8be942302ef491a7d8253e0dde7
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e4aa2c08e6ee4954\n    [X-B3-Traceid] => 689e44463ef92663061ebab64f96373e\n    [B3] => 689e44463ef92663061ebab64f96373e-e4aa2c08e6ee4954-1\n    [Traceparent] => 00-689e44463ef92663061ebab64f96373e-e4aa2c08e6ee4954-01\n    [X-Amzn-Trace-Id] => Root=1-689e4446-3ef92663061ebab64f96373e;Parent=e4aa2c08e6ee4954;Sampled=1\n    [X-Adsk-Signature] => sha256=3b65d791e503b04e1cf0fca3824f85145255c63c655c5e444e00b00d0fb1d137\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202630401-63361092037641\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202630401-63361092037641","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63361092037641","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:17:10.401Z"},"publishedAt":"2025-08-14T20:17:10.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:17:14
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:36]  Provided signature: sha256=f9906b5929bad548b9e6977cf933e9b0b4e3e8be942302ef491a7d8253e0dde7
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:37]  Calculated signature: sha256=f9906b5929bad548b9e6977cf933e9b0b4e3e8be942302ef491a7d8253e0dde7
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b84ea64cb789e247\n    [X-B3-Traceid] => 689e44463ef92663061ebab64f96373e\n    [B3] => 689e44463ef92663061ebab64f96373e-b84ea64cb789e247-1\n    [Traceparent] => 00-689e44463ef92663061ebab64f96373e-b84ea64cb789e247-01\n    [X-Amzn-Trace-Id] => Root=1-689e4446-3ef92663061ebab64f96373e;Parent=b84ea64cb789e247;Sampled=1\n    [X-Adsk-Signature] => sha256=f9906b5929bad548b9e6977cf933e9b0b4e3e8be942302ef491a7d8253e0dde7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202630401-63361092037641\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:17:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202630401-63361092037641","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63361092037641","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:17:10.401Z"},"publishedAt":"2025-08-14T20:17:10.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:18:50
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:36]  Provided signature: sha256=0fcc362d24570b791742872087ffc2da53e09844f7bafd6010502cb88ebd8c41
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:37]  Calculated signature: sha256=7cb1be7aad7b1c796fca7855780dacff700d7ee739c6f0b5d0c56cc7db2d7a23
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0e3d872ac4114b8a\n    [X-B3-Traceid] => 689e44a606ef93d90aeae09676a074fc\n    [B3] => 689e44a606ef93d90aeae09676a074fc-0e3d872ac4114b8a-1\n    [Traceparent] => 00-689e44a606ef93d90aeae09676a074fc-0e3d872ac4114b8a-01\n    [X-Amzn-Trace-Id] => Root=1-689e44a6-06ef93d90aeae09676a074fc;Parent=0e3d872ac4114b8a;Sampled=1\n    [X-Adsk-Signature] => sha256=0fcc362d24570b791742872087ffc2da53e09844f7bafd6010502cb88ebd8c41\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202726555-66264904812166\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202726555-66264904812166","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:18:46.555Z"},"publishedAt":"2025-08-14T20:18:46.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:18:50
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:36]  Provided signature: sha256=7cb1be7aad7b1c796fca7855780dacff700d7ee739c6f0b5d0c56cc7db2d7a23
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:37]  Calculated signature: sha256=7cb1be7aad7b1c796fca7855780dacff700d7ee739c6f0b5d0c56cc7db2d7a23
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 40cd88c0520ab3ba\n    [X-B3-Traceid] => 689e44a606ef93d90aeae09676a074fc\n    [B3] => 689e44a606ef93d90aeae09676a074fc-40cd88c0520ab3ba-1\n    [Traceparent] => 00-689e44a606ef93d90aeae09676a074fc-40cd88c0520ab3ba-01\n    [X-Amzn-Trace-Id] => Root=1-689e44a6-06ef93d90aeae09676a074fc;Parent=40cd88c0520ab3ba;Sampled=1\n    [X-Adsk-Signature] => sha256=7cb1be7aad7b1c796fca7855780dacff700d7ee739c6f0b5d0c56cc7db2d7a23\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202726555-66264904812166\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:18:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202726555-66264904812166","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:18:46.555Z"},"publishedAt":"2025-08-14T20:18:46.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:19:15
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:36]  Provided signature: sha256=757f597c1ebae3295aa7c61ff9336bc36ac185296d5f092df9258fe2b07cb22d
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:37]  Calculated signature: sha256=757f597c1ebae3295aa7c61ff9336bc36ac185296d5f092df9258fe2b07cb22d
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4eeea9c793537789\n    [X-B3-Traceid] => 689e44c0784df8bb619ce70d3d59dafe\n    [B3] => 689e44c0784df8bb619ce70d3d59dafe-4eeea9c793537789-1\n    [Traceparent] => 00-689e44c0784df8bb619ce70d3d59dafe-4eeea9c793537789-01\n    [X-Amzn-Trace-Id] => Root=1-689e44c0-784df8bb619ce70d3d59dafe;Parent=4eeea9c793537789;Sampled=1\n    [X-Adsk-Signature] => sha256=757f597c1ebae3295aa7c61ff9336bc36ac185296d5f092df9258fe2b07cb22d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202752499-63247464085385\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202752499-63247464085385","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63247464085385","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:19:12.499Z"},"publishedAt":"2025-08-14T20:19:12.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:19:15
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:36]  Provided signature: sha256=a1365cead783d928c4c5819df34054f1cf3241a63a0c7c6015fb00bceacd7ab7
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:37]  Calculated signature: sha256=757f597c1ebae3295aa7c61ff9336bc36ac185296d5f092df9258fe2b07cb22d
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 58abe9c607cb6241\n    [X-B3-Traceid] => 689e44c0784df8bb619ce70d3d59dafe\n    [B3] => 689e44c0784df8bb619ce70d3d59dafe-58abe9c607cb6241-1\n    [Traceparent] => 00-689e44c0784df8bb619ce70d3d59dafe-58abe9c607cb6241-01\n    [X-Amzn-Trace-Id] => Root=1-689e44c0-784df8bb619ce70d3d59dafe;Parent=58abe9c607cb6241;Sampled=1\n    [X-Adsk-Signature] => sha256=a1365cead783d928c4c5819df34054f1cf3241a63a0c7c6015fb00bceacd7ab7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202752499-63247464085385\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:19:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202752499-63247464085385","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63247464085385","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:19:12.499Z"},"publishedAt":"2025-08-14T20:19:12.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:23:03
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:36]  Provided signature: sha256=0866b260b7141c574ea41048c5c20e85049b3ec26d4e0b2e72a12210b043d241
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:37]  Calculated signature: sha256=f6d121d9ffb05938ffe39ad22880ea5b361d63d265bd0968c7481e6b8b849d4d
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 53ddfb7e4f8f7472\n    [X-B3-Traceid] => 689e45a461dd92665ef1707c63bae29d\n    [B3] => 689e45a461dd92665ef1707c63bae29d-53ddfb7e4f8f7472-1\n    [Traceparent] => 00-689e45a461dd92665ef1707c63bae29d-53ddfb7e4f8f7472-01\n    [X-Amzn-Trace-Id] => Root=1-689e45a4-61dd92665ef1707c63bae29d;Parent=53ddfb7e4f8f7472;Sampled=1\n    [X-Adsk-Signature] => sha256=0866b260b7141c574ea41048c5c20e85049b3ec26d4e0b2e72a12210b043d241\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202980239-59792274819899\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202980239-59792274819899","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59792274819899","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:23:00.239Z"},"publishedAt":"2025-08-14T20:23:00.000Z","csn":"5103159758"}
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-14 20:23:03
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:36]  Provided signature: sha256=f6d121d9ffb05938ffe39ad22880ea5b361d63d265bd0968c7481e6b8b849d4d
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:37]  Calculated signature: sha256=f6d121d9ffb05938ffe39ad22880ea5b361d63d265bd0968c7481e6b8b849d4d
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d5eebe5850644f49\n    [X-B3-Traceid] => 689e45a461dd92665ef1707c63bae29d\n    [B3] => 689e45a461dd92665ef1707c63bae29d-d5eebe5850644f49-1\n    [Traceparent] => 00-689e45a461dd92665ef1707c63bae29d-d5eebe5850644f49-01\n    [X-Amzn-Trace-Id] => Root=1-689e45a4-61dd92665ef1707c63bae29d;Parent=d5eebe5850644f49;Sampled=1\n    [X-Adsk-Signature] => sha256=f6d121d9ffb05938ffe39ad22880ea5b361d63d265bd0968c7481e6b8b849d4d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755202980239-59792274819899\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-14 20:23:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755202980239-59792274819899","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59792274819899","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-14T20:23:00.239Z"},"publishedAt":"2025-08-14T20:23:00.000Z","csn":"5103159758"}
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 00:06:14
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:36]  Provided signature: sha256=39640692f3b9de215879f9475489153d63b24876fc779dd79737dc4793b57368
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:37]  Calculated signature: sha256=39640692f3b9de215879f9475489153d63b24876fc779dd79737dc4793b57368
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 46aeb4ec45d937eb\n    [X-B3-Traceid] => 689e79f351360c8f0f24fb65566c715c\n    [B3] => 689e79f351360c8f0f24fb65566c715c-46aeb4ec45d937eb-1\n    [Traceparent] => 00-689e79f351360c8f0f24fb65566c715c-46aeb4ec45d937eb-01\n    [X-Amzn-Trace-Id] => Root=1-689e79f3-51360c8f0f24fb65566c715c;Parent=46aeb4ec45d937eb;Sampled=1\n    [X-Adsk-Signature] => sha256=39640692f3b9de215879f9475489153d63b24876fc779dd79737dc4793b57368\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1307e69e-d218-4d72-9f16-143586187c54\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1307e69e-d218-4d72-9f16-143586187c54","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75368953253895","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-14T23:41:08.000+0000"},"publishedAt":"2025-08-15T00:06:11.000Z","csn":"5103159758"}
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 00:06:14
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:36]  Provided signature: sha256=264acd3bb75105e274bb21cd8cebc6e9ab9b6b64f96386777b040b655e4d1736
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:37]  Calculated signature: sha256=39640692f3b9de215879f9475489153d63b24876fc779dd79737dc4793b57368
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bcaf8d5d894a0068\n    [X-B3-Traceid] => 689e79f351360c8f0f24fb65566c715c\n    [B3] => 689e79f351360c8f0f24fb65566c715c-bcaf8d5d894a0068-1\n    [Traceparent] => 00-689e79f351360c8f0f24fb65566c715c-bcaf8d5d894a0068-01\n    [X-Amzn-Trace-Id] => Root=1-689e79f3-51360c8f0f24fb65566c715c;Parent=bcaf8d5d894a0068;Sampled=1\n    [X-Adsk-Signature] => sha256=264acd3bb75105e274bb21cd8cebc6e9ab9b6b64f96386777b040b655e4d1736\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1307e69e-d218-4d72-9f16-143586187c54\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 00:06:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1307e69e-d218-4d72-9f16-143586187c54","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75368953253895","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-14T23:41:08.000+0000"},"publishedAt":"2025-08-15T00:06:11.000Z","csn":"5103159758"}
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 00:10:45
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:36]  Provided signature: sha256=0c1f9c68c3488d84c997942a182500a01a3d1e998bba6bc499f3c650335e6198
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:37]  Calculated signature: sha256=0c1f9c68c3488d84c997942a182500a01a3d1e998bba6bc499f3c650335e6198
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 58d3ad46f0a94592\n    [X-B3-Traceid] => 689e7b0360e073e52d7b8b1f5658b10e\n    [B3] => 689e7b0360e073e52d7b8b1f5658b10e-58d3ad46f0a94592-1\n    [Traceparent] => 00-689e7b0360e073e52d7b8b1f5658b10e-58d3ad46f0a94592-01\n    [X-Amzn-Trace-Id] => Root=1-689e7b03-60e073e52d7b8b1f5658b10e;Parent=58d3ad46f0a94592;Sampled=1\n    [X-Adsk-Signature] => sha256=0c1f9c68c3488d84c997942a182500a01a3d1e998bba6bc499f3c650335e6198\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bfcc0901-3aae-48b8-9da6-32eca330ade7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"bfcc0901-3aae-48b8-9da6-32eca330ade7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-14T23:40:40.000+0000"},"publishedAt":"2025-08-15T00:10:43.000Z","csn":"5103159758"}
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 00:10:45
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:36]  Provided signature: sha256=65144b3cfe853fed6d07f04be145e1678fddce4bacd230dac062fbbec9c33a82
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:37]  Calculated signature: sha256=0c1f9c68c3488d84c997942a182500a01a3d1e998bba6bc499f3c650335e6198
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0412637630a74787\n    [X-B3-Traceid] => 689e7b0360e073e52d7b8b1f5658b10e\n    [B3] => 689e7b0360e073e52d7b8b1f5658b10e-0412637630a74787-1\n    [Traceparent] => 00-689e7b0360e073e52d7b8b1f5658b10e-0412637630a74787-01\n    [X-Amzn-Trace-Id] => Root=1-689e7b03-60e073e52d7b8b1f5658b10e;Parent=0412637630a74787;Sampled=1\n    [X-Adsk-Signature] => sha256=65144b3cfe853fed6d07f04be145e1678fddce4bacd230dac062fbbec9c33a82\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bfcc0901-3aae-48b8-9da6-32eca330ade7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 00:10:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"bfcc0901-3aae-48b8-9da6-32eca330ade7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-14T23:40:40.000+0000"},"publishedAt":"2025-08-15T00:10:43.000Z","csn":"5103159758"}
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 06:44:32
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:36]  Provided signature: sha256=f8200c0aa503a4b95fd39237eaac2727b5ea6a460b1ab5a21afe15ad375ce9f2
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:37]  Calculated signature: sha256=fa512e94b9b4d10582e173f8688457f2d488d026a56cb0e9712d51df8f515339
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d856a5593e5e856c\n    [X-B3-Traceid] => 689ed74e30609e3477f0c4a90c9f0ffa\n    [B3] => 689ed74e30609e3477f0c4a90c9f0ffa-d856a5593e5e856c-1\n    [Traceparent] => 00-689ed74e30609e3477f0c4a90c9f0ffa-d856a5593e5e856c-01\n    [X-Amzn-Trace-Id] => Root=1-689ed74e-30609e3477f0c4a90c9f0ffa;Parent=d856a5593e5e856c;Sampled=1\n    [X-Adsk-Signature] => sha256=f8200c0aa503a4b95fd39237eaac2727b5ea6a460b1ab5a21afe15ad375ce9f2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 04b5c5aa-77f1-4fc1-ad0a-a3dc940eacde\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"04b5c5aa-77f1-4fc1-ad0a-a3dc940eacde","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62521806254194","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-15T06:29:27.000+0000"},"publishedAt":"2025-08-15T06:44:30.000Z","csn":"5103159758"}
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 06:44:32
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:36]  Provided signature: sha256=fa512e94b9b4d10582e173f8688457f2d488d026a56cb0e9712d51df8f515339
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:37]  Calculated signature: sha256=fa512e94b9b4d10582e173f8688457f2d488d026a56cb0e9712d51df8f515339
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ab2fbd528a410de3\n    [X-B3-Traceid] => 689ed74e30609e3477f0c4a90c9f0ffa\n    [B3] => 689ed74e30609e3477f0c4a90c9f0ffa-ab2fbd528a410de3-1\n    [Traceparent] => 00-689ed74e30609e3477f0c4a90c9f0ffa-ab2fbd528a410de3-01\n    [X-Amzn-Trace-Id] => Root=1-689ed74e-30609e3477f0c4a90c9f0ffa;Parent=ab2fbd528a410de3;Sampled=1\n    [X-Adsk-Signature] => sha256=fa512e94b9b4d10582e173f8688457f2d488d026a56cb0e9712d51df8f515339\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 04b5c5aa-77f1-4fc1-ad0a-a3dc940eacde\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 06:44:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"04b5c5aa-77f1-4fc1-ad0a-a3dc940eacde","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62521806254194","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-15T06:29:27.000+0000"},"publishedAt":"2025-08-15T06:44:30.000Z","csn":"5103159758"}
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 08:51:01
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:36]  Provided signature: sha256=412481977e59bc96073fe895cc01b2f4dde527d8aca3b7df5b711435610677b5
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:37]  Calculated signature: sha256=412481977e59bc96073fe895cc01b2f4dde527d8aca3b7df5b711435610677b5
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0402628377ac9ca9\n    [X-B3-Traceid] => 689ef4f31e8551adb2e8bb1ff9e49f4b\n    [B3] => 689ef4f31e8551adb2e8bb1ff9e49f4b-0402628377ac9ca9-1\n    [Traceparent] => 00-689ef4f31e8551adb2e8bb1ff9e49f4b-0402628377ac9ca9-01\n    [X-Amzn-Trace-Id] => Root=1-689ef4f3-1e8551adb2e8bb1ff9e49f4b;Parent=0402628377ac9ca9;Sampled=1\n    [X-Adsk-Signature] => sha256=412481977e59bc96073fe895cc01b2f4dde527d8aca3b7df5b711435610677b5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9e41cc9d-3b4b-4005-b8cd-395be2eda5a4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"9e41cc9d-3b4b-4005-b8cd-395be2eda5a4","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Draft","message":"Quote# Q-1008618 status changed to Draft.","modifiedAt":"2025-08-15T08:50:59.055Z"},"publishedAt":"2025-08-15T08:50:59.000Z","csn":"5103159758"}
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 08:51:01
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:36]  Provided signature: sha256=c874b33fa34b514e8637d9153d0ded56abb4abb9f7a37faa6cf4a9fde5f0d6e0
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:37]  Calculated signature: sha256=412481977e59bc96073fe895cc01b2f4dde527d8aca3b7df5b711435610677b5
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 506ec628d30b3689\n    [X-B3-Traceid] => 689ef4f31e8551adb2e8bb1ff9e49f4b\n    [B3] => 689ef4f31e8551adb2e8bb1ff9e49f4b-506ec628d30b3689-1\n    [Traceparent] => 00-689ef4f31e8551adb2e8bb1ff9e49f4b-506ec628d30b3689-01\n    [X-Amzn-Trace-Id] => Root=1-689ef4f3-1e8551adb2e8bb1ff9e49f4b;Parent=506ec628d30b3689;Sampled=1\n    [X-Adsk-Signature] => sha256=c874b33fa34b514e8637d9153d0ded56abb4abb9f7a37faa6cf4a9fde5f0d6e0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9e41cc9d-3b4b-4005-b8cd-395be2eda5a4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 08:51:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"9e41cc9d-3b4b-4005-b8cd-395be2eda5a4","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Draft","message":"Quote# Q-1008618 status changed to Draft.","modifiedAt":"2025-08-15T08:50:59.055Z"},"publishedAt":"2025-08-15T08:50:59.000Z","csn":"5103159758"}
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 08:51:54
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:36]  Provided signature: sha256=8cc621af1db39a1338a7472285fa43d0399c3bb6f3f4c4269b8924b0d8d0988a
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:37]  Calculated signature: sha256=4c38415c4c59419e8135591d8b3e85f9db355a3cd79f88bd89fb11ed778e8947
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 70366250582fba72\n    [X-B3-Traceid] => 689ef528b19297064d62237784ea550a\n    [B3] => 689ef528b19297064d62237784ea550a-70366250582fba72-1\n    [Traceparent] => 00-689ef528b19297064d62237784ea550a-70366250582fba72-01\n    [X-Amzn-Trace-Id] => Root=1-689ef528-b19297064d62237784ea550a;Parent=70366250582fba72;Sampled=1\n    [X-Adsk-Signature] => sha256=8cc621af1db39a1338a7472285fa43d0399c3bb6f3f4c4269b8924b0d8d0988a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ed6adefd-8ec8-498a-ae03-0dd3b9563f3d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"ed6adefd-8ec8-498a-ae03-0dd3b9563f3d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Quoted","message":"Quote# Q-1008618 status changed to Quoted.","modifiedAt":"2025-08-15T08:51:51.933Z"},"publishedAt":"2025-08-15T08:51:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 08:51:54
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:36]  Provided signature: sha256=4c38415c4c59419e8135591d8b3e85f9db355a3cd79f88bd89fb11ed778e8947
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:37]  Calculated signature: sha256=4c38415c4c59419e8135591d8b3e85f9db355a3cd79f88bd89fb11ed778e8947
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b516891f188a44ec\n    [X-B3-Traceid] => 689ef528b19297064d62237784ea550a\n    [B3] => 689ef528b19297064d62237784ea550a-b516891f188a44ec-1\n    [Traceparent] => 00-689ef528b19297064d62237784ea550a-b516891f188a44ec-01\n    [X-Amzn-Trace-Id] => Root=1-689ef528-b19297064d62237784ea550a;Parent=b516891f188a44ec;Sampled=1\n    [X-Adsk-Signature] => sha256=4c38415c4c59419e8135591d8b3e85f9db355a3cd79f88bd89fb11ed778e8947\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ed6adefd-8ec8-498a-ae03-0dd3b9563f3d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 08:51:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"ed6adefd-8ec8-498a-ae03-0dd3b9563f3d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Quoted","message":"Quote# Q-1008618 status changed to Quoted.","modifiedAt":"2025-08-15T08:51:51.933Z"},"publishedAt":"2025-08-15T08:51:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 09:37:43
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:36]  Provided signature: sha256=9f1b54ebee6d9f962e13678c3200da79ab6f31ca67473accc6d374cc984ae9b7
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:37]  Calculated signature: sha256=283f672fb968da4453f84facad7bb1beab7f8f6a6cd26fe9523de0a32f899fe6
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 295d9fb21fefb9cc\n    [X-B3-Traceid] => 689effe513862c2e4b56d82357afb676\n    [B3] => 689effe513862c2e4b56d82357afb676-295d9fb21fefb9cc-1\n    [Traceparent] => 00-689effe513862c2e4b56d82357afb676-295d9fb21fefb9cc-01\n    [X-Amzn-Trace-Id] => Root=1-689effe5-13862c2e4b56d82357afb676;Parent=295d9fb21fefb9cc;Sampled=1\n    [X-Adsk-Signature] => sha256=9f1b54ebee6d9f962e13678c3200da79ab6f31ca67473accc6d374cc984ae9b7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a29b230a-8c5f-4b53-8082-44052e3fd19e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"a29b230a-8c5f-4b53-8082-44052e3fd19e","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75509849291012","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T09:17:38.000+0000"},"publishedAt":"2025-08-15T09:37:41.000Z","csn":"5103159758"}
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 09:37:43
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:36]  Provided signature: sha256=283f672fb968da4453f84facad7bb1beab7f8f6a6cd26fe9523de0a32f899fe6
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:37]  Calculated signature: sha256=283f672fb968da4453f84facad7bb1beab7f8f6a6cd26fe9523de0a32f899fe6
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d1306672df8e21ba\n    [X-B3-Traceid] => 689effe513862c2e4b56d82357afb676\n    [B3] => 689effe513862c2e4b56d82357afb676-d1306672df8e21ba-1\n    [Traceparent] => 00-689effe513862c2e4b56d82357afb676-d1306672df8e21ba-01\n    [X-Amzn-Trace-Id] => Root=1-689effe5-13862c2e4b56d82357afb676;Parent=d1306672df8e21ba;Sampled=1\n    [X-Adsk-Signature] => sha256=283f672fb968da4453f84facad7bb1beab7f8f6a6cd26fe9523de0a32f899fe6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a29b230a-8c5f-4b53-8082-44052e3fd19e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 09:37:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"a29b230a-8c5f-4b53-8082-44052e3fd19e","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75509849291012","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T09:17:38.000+0000"},"publishedAt":"2025-08-15T09:37:41.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:21:08
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:36]  Provided signature: sha256=c6782205b9e039e15ef6ae87ea8c6f925846908d9758bbd3aa5a832f7eda7f5a
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:37]  Calculated signature: sha256=c6782205b9e039e15ef6ae87ea8c6f925846908d9758bbd3aa5a832f7eda7f5a
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a8c558a2302e5d78\n    [X-B3-Traceid] => 689f0a1234a80d3ee8eb7e7ea9d13dfa\n    [B3] => 689f0a1234a80d3ee8eb7e7ea9d13dfa-a8c558a2302e5d78-1\n    [Traceparent] => 00-689f0a1234a80d3ee8eb7e7ea9d13dfa-a8c558a2302e5d78-01\n    [X-Amzn-Trace-Id] => Root=1-689f0a12-34a80d3ee8eb7e7ea9d13dfa;Parent=a8c558a2302e5d78;Sampled=1\n    [X-Adsk-Signature] => sha256=c6782205b9e039e15ef6ae87ea8c6f925846908d9758bbd3aa5a832f7eda7f5a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bd4c0361-afaa-48d2-8cbe-27a0d95a026d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"bd4c0361-afaa-48d2-8cbe-27a0d95a026d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008728","transactionId":"35a8b854-ddc9-5b45-9e5b-d94dd1f75d44","quoteStatus":"Draft","message":"Quote# Q-1008728 status changed to Draft.","modifiedAt":"2025-08-15T10:21:05.814Z"},"publishedAt":"2025-08-15T10:21:06.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:21:08
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:36]  Provided signature: sha256=5175dc0f1c169ad7046f9c1ac7b7083cd4361cd6a2a792b9ba4fa3364194288b
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:37]  Calculated signature: sha256=c6782205b9e039e15ef6ae87ea8c6f925846908d9758bbd3aa5a832f7eda7f5a
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e1e3895ac5b1b2fe\n    [X-B3-Traceid] => 689f0a1234a80d3ee8eb7e7ea9d13dfa\n    [B3] => 689f0a1234a80d3ee8eb7e7ea9d13dfa-e1e3895ac5b1b2fe-1\n    [Traceparent] => 00-689f0a1234a80d3ee8eb7e7ea9d13dfa-e1e3895ac5b1b2fe-01\n    [X-Amzn-Trace-Id] => Root=1-689f0a12-34a80d3ee8eb7e7ea9d13dfa;Parent=e1e3895ac5b1b2fe;Sampled=1\n    [X-Adsk-Signature] => sha256=5175dc0f1c169ad7046f9c1ac7b7083cd4361cd6a2a792b9ba4fa3364194288b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bd4c0361-afaa-48d2-8cbe-27a0d95a026d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:21:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"bd4c0361-afaa-48d2-8cbe-27a0d95a026d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008728","transactionId":"35a8b854-ddc9-5b45-9e5b-d94dd1f75d44","quoteStatus":"Draft","message":"Quote# Q-1008728 status changed to Draft.","modifiedAt":"2025-08-15T10:21:05.814Z"},"publishedAt":"2025-08-15T10:21:06.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:21:36
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:36]  Provided signature: sha256=85008b07321b7c55793b6e1cb2153bcc282da1f26a0fc472d9415c54e5bdcc4f
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:37]  Calculated signature: sha256=4c88b0fa0991bb74db4db25a3579758f92b197b060f933ea926748d7709a5b7d
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c1689f07246c11a2\n    [X-B3-Traceid] => 689f0a2d5b760d9a7a0cbe85ea59c792\n    [B3] => 689f0a2d5b760d9a7a0cbe85ea59c792-c1689f07246c11a2-1\n    [Traceparent] => 00-689f0a2d5b760d9a7a0cbe85ea59c792-c1689f07246c11a2-01\n    [X-Amzn-Trace-Id] => Root=1-689f0a2d-5b760d9a7a0cbe85ea59c792;Parent=c1689f07246c11a2;Sampled=1\n    [X-Adsk-Signature] => sha256=85008b07321b7c55793b6e1cb2153bcc282da1f26a0fc472d9415c54e5bdcc4f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0d1be9fc-c8f6-43eb-ad27-f74e519c585f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"0d1be9fc-c8f6-43eb-ad27-f74e519c585f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008728","transactionId":"35a8b854-ddc9-5b45-9e5b-d94dd1f75d44","quoteStatus":"Quoted","message":"Quote# Q-1008728 status changed to Quoted.","modifiedAt":"2025-08-15T10:21:33.018Z"},"publishedAt":"2025-08-15T10:21:33.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:21:36
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:36]  Provided signature: sha256=4c88b0fa0991bb74db4db25a3579758f92b197b060f933ea926748d7709a5b7d
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:37]  Calculated signature: sha256=4c88b0fa0991bb74db4db25a3579758f92b197b060f933ea926748d7709a5b7d
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cd67d6cd72e96790\n    [X-B3-Traceid] => 689f0a2d5b760d9a7a0cbe85ea59c792\n    [B3] => 689f0a2d5b760d9a7a0cbe85ea59c792-cd67d6cd72e96790-1\n    [Traceparent] => 00-689f0a2d5b760d9a7a0cbe85ea59c792-cd67d6cd72e96790-01\n    [X-Amzn-Trace-Id] => Root=1-689f0a2d-5b760d9a7a0cbe85ea59c792;Parent=cd67d6cd72e96790;Sampled=1\n    [X-Adsk-Signature] => sha256=4c88b0fa0991bb74db4db25a3579758f92b197b060f933ea926748d7709a5b7d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0d1be9fc-c8f6-43eb-ad27-f74e519c585f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:21:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"0d1be9fc-c8f6-43eb-ad27-f74e519c585f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008728","transactionId":"35a8b854-ddc9-5b45-9e5b-d94dd1f75d44","quoteStatus":"Quoted","message":"Quote# Q-1008728 status changed to Quoted.","modifiedAt":"2025-08-15T10:21:33.018Z"},"publishedAt":"2025-08-15T10:21:33.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:31:59
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:36]  Provided signature: sha256=076f0378c2144bae8fb422a805a72b04d19b782531e4ef8360acf022689e859b
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:37]  Calculated signature: sha256=b89fac3c816539f69d48db3dbb449d6f2f576a99325cb682a8796f8e7e1e06fc
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5511841fd750b92d\n    [X-B3-Traceid] => 689f0c9cb544e36219a034498b92096d\n    [B3] => 689f0c9cb544e36219a034498b92096d-5511841fd750b92d-1\n    [Traceparent] => 00-689f0c9cb544e36219a034498b92096d-5511841fd750b92d-01\n    [X-Amzn-Trace-Id] => Root=1-689f0c9c-b544e36219a034498b92096d;Parent=5511841fd750b92d;Sampled=1\n    [X-Adsk-Signature] => sha256=076f0378c2144bae8fb422a805a72b04d19b782531e4ef8360acf022689e859b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 26e812fd-86ae-489c-ba83-58b96eef1f1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"26e812fd-86ae-489c-ba83-58b96eef1f1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988001","transactionId":"6b16c5d9-1a00-52bc-ad64-a4d468d3b383","quoteStatus":"Order Submitted","message":"Quote# Q-988001 status changed to Order Submitted.","modifiedAt":"2025-08-15T10:31:56.032Z"},"publishedAt":"2025-08-15T10:31:56.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:31:59
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:36]  Provided signature: sha256=b89fac3c816539f69d48db3dbb449d6f2f576a99325cb682a8796f8e7e1e06fc
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:37]  Calculated signature: sha256=b89fac3c816539f69d48db3dbb449d6f2f576a99325cb682a8796f8e7e1e06fc
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d88cd25003df403f\n    [X-B3-Traceid] => 689f0c9cb544e36219a034498b92096d\n    [B3] => 689f0c9cb544e36219a034498b92096d-d88cd25003df403f-1\n    [Traceparent] => 00-689f0c9cb544e36219a034498b92096d-d88cd25003df403f-01\n    [X-Amzn-Trace-Id] => Root=1-689f0c9c-b544e36219a034498b92096d;Parent=d88cd25003df403f;Sampled=1\n    [X-Adsk-Signature] => sha256=b89fac3c816539f69d48db3dbb449d6f2f576a99325cb682a8796f8e7e1e06fc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 26e812fd-86ae-489c-ba83-58b96eef1f1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:31:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"26e812fd-86ae-489c-ba83-58b96eef1f1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988001","transactionId":"6b16c5d9-1a00-52bc-ad64-a4d468d3b383","quoteStatus":"Order Submitted","message":"Quote# Q-988001 status changed to Order Submitted.","modifiedAt":"2025-08-15T10:31:56.032Z"},"publishedAt":"2025-08-15T10:31:56.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:32:12
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:36]  Provided signature: sha256=4509f24c1c8192cc47bc24800413bcd3d93fbe5167bad55d0b6c5f5948ce7080
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:37]  Calculated signature: sha256=0494d01ef154c42deeb0e989356f2deed72f0f743d3e6b3925556075d3593652
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e7680c6ece9947e2\n    [X-B3-Traceid] => 689f0caa78bb5ae658f352c2e3ec1dbf\n    [B3] => 689f0caa78bb5ae658f352c2e3ec1dbf-e7680c6ece9947e2-1\n    [Traceparent] => 00-689f0caa78bb5ae658f352c2e3ec1dbf-e7680c6ece9947e2-01\n    [X-Amzn-Trace-Id] => Root=1-689f0caa-78bb5ae658f352c2e3ec1dbf;Parent=e7680c6ece9947e2;Sampled=1\n    [X-Adsk-Signature] => sha256=4509f24c1c8192cc47bc24800413bcd3d93fbe5167bad55d0b6c5f5948ce7080\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5fb1136f-8abc-485b-bbd9-755c81cd334c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"5fb1136f-8abc-485b-bbd9-755c81cd334c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988001","transactionId":"6b16c5d9-1a00-52bc-ad64-a4d468d3b383","quoteStatus":"Ordered","message":"Quote# Q-988001 status changed to Ordered.","modifiedAt":"2025-08-15T10:32:09.774Z"},"publishedAt":"2025-08-15T10:32:10.000Z","csn":"5103159758"}
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 10:32:12
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:36]  Provided signature: sha256=0494d01ef154c42deeb0e989356f2deed72f0f743d3e6b3925556075d3593652
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:37]  Calculated signature: sha256=0494d01ef154c42deeb0e989356f2deed72f0f743d3e6b3925556075d3593652
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 945b2da607006373\n    [X-B3-Traceid] => 689f0caa78bb5ae658f352c2e3ec1dbf\n    [B3] => 689f0caa78bb5ae658f352c2e3ec1dbf-945b2da607006373-1\n    [Traceparent] => 00-689f0caa78bb5ae658f352c2e3ec1dbf-945b2da607006373-01\n    [X-Amzn-Trace-Id] => Root=1-689f0caa-78bb5ae658f352c2e3ec1dbf;Parent=945b2da607006373;Sampled=1\n    [X-Adsk-Signature] => sha256=0494d01ef154c42deeb0e989356f2deed72f0f743d3e6b3925556075d3593652\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5fb1136f-8abc-485b-bbd9-755c81cd334c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 10:32:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"5fb1136f-8abc-485b-bbd9-755c81cd334c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988001","transactionId":"6b16c5d9-1a00-52bc-ad64-a4d468d3b383","quoteStatus":"Ordered","message":"Quote# Q-988001 status changed to Ordered.","modifiedAt":"2025-08-15T10:32:09.774Z"},"publishedAt":"2025-08-15T10:32:10.000Z","csn":"5103159758"}
[webhook] [2025-08-15 11:35:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 11:35:56
[webhook] [2025-08-15 11:35:56] [adwsapi_v2.php:36]  Provided signature: sha256=d0c699314e1d8a2658833aa4e425455c9cf2351c96c177bbd0256f80bb472285
[webhook] [2025-08-15 11:35:56] [adwsapi_v2.php:37]  Calculated signature: sha256=d1b376f5dc39e479fe3fb749e9741d9fc05ee2b61d993ac6bfce5f1fd40d91fb
[webhook] [2025-08-15 11:35:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c19e37d0ef130070\n    [X-B3-Traceid] => 689f1b9a3a3f52b8773b087b67936cfe\n    [B3] => 689f1b9a3a3f52b8773b087b67936cfe-c19e37d0ef130070-1\n    [Traceparent] => 00-689f1b9a3a3f52b8773b087b67936cfe-c19e37d0ef130070-01\n    [X-Amzn-Trace-Id] => Root=1-689f1b9a-3a3f52b8773b087b67936cfe;Parent=c19e37d0ef130070;Sampled=1\n    [X-Adsk-Signature] => sha256=d0c699314e1d8a2658833aa4e425455c9cf2351c96c177bbd0256f80bb472285\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fd7810be-4fbf-4f81-8586-a4f7c1302c9f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 11:35:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"fd7810be-4fbf-4f81-8586-a4f7c1302c9f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75525391314639","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T11:20:48.000+0000"},"publishedAt":"2025-08-15T11:35:54.000Z","csn":"5103159758"}
[webhook] [2025-08-15 11:35:57] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 11:35:57
[webhook] [2025-08-15 11:35:57] [adwsapi_v2.php:36]  Provided signature: sha256=d1b376f5dc39e479fe3fb749e9741d9fc05ee2b61d993ac6bfce5f1fd40d91fb
[webhook] [2025-08-15 11:35:57] [adwsapi_v2.php:37]  Calculated signature: sha256=d1b376f5dc39e479fe3fb749e9741d9fc05ee2b61d993ac6bfce5f1fd40d91fb
[webhook] [2025-08-15 11:35:57] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 74423d88f86e86e4\n    [X-B3-Traceid] => 689f1b9a3a3f52b8773b087b67936cfe\n    [B3] => 689f1b9a3a3f52b8773b087b67936cfe-74423d88f86e86e4-1\n    [Traceparent] => 00-689f1b9a3a3f52b8773b087b67936cfe-74423d88f86e86e4-01\n    [X-Amzn-Trace-Id] => Root=1-689f1b9a-3a3f52b8773b087b67936cfe;Parent=74423d88f86e86e4;Sampled=1\n    [X-Adsk-Signature] => sha256=d1b376f5dc39e479fe3fb749e9741d9fc05ee2b61d993ac6bfce5f1fd40d91fb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fd7810be-4fbf-4f81-8586-a4f7c1302c9f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 11:35:57] [adwsapi_v2.php:57]  Received webhook data: {"id":"fd7810be-4fbf-4f81-8586-a4f7c1302c9f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75525391314639","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T11:20:48.000+0000"},"publishedAt":"2025-08-15T11:35:54.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:08:00
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:36]  Provided signature: sha256=eebcdd38007b62f822834c377733f993400daa88204814128871871a660e3614
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:37]  Calculated signature: sha256=f939b1653105d59f012b79e442abbe78af3954fb768c6d8562d5c853e288f14e
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8b5355f013a455e0\n    [X-B3-Traceid] => 689f231e1b09c6aa023b23ba1d6fe61b\n    [B3] => 689f231e1b09c6aa023b23ba1d6fe61b-8b5355f013a455e0-1\n    [Traceparent] => 00-689f231e1b09c6aa023b23ba1d6fe61b-8b5355f013a455e0-01\n    [X-Amzn-Trace-Id] => Root=1-689f231e-1b09c6aa023b23ba1d6fe61b;Parent=8b5355f013a455e0;Sampled=1\n    [X-Adsk-Signature] => sha256=eebcdd38007b62f822834c377733f993400daa88204814128871871a660e3614\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755259678565-75265394168690\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755259678565-75265394168690","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75265394168690","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-15T12:07:58.565Z"},"publishedAt":"2025-08-15T12:07:58.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:08:00
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:36]  Provided signature: sha256=f939b1653105d59f012b79e442abbe78af3954fb768c6d8562d5c853e288f14e
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:37]  Calculated signature: sha256=f939b1653105d59f012b79e442abbe78af3954fb768c6d8562d5c853e288f14e
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 23dad9c9b7aedc20\n    [X-B3-Traceid] => 689f231e1b09c6aa023b23ba1d6fe61b\n    [B3] => 689f231e1b09c6aa023b23ba1d6fe61b-23dad9c9b7aedc20-1\n    [Traceparent] => 00-689f231e1b09c6aa023b23ba1d6fe61b-23dad9c9b7aedc20-01\n    [X-Amzn-Trace-Id] => Root=1-689f231e-1b09c6aa023b23ba1d6fe61b;Parent=23dad9c9b7aedc20;Sampled=1\n    [X-Adsk-Signature] => sha256=f939b1653105d59f012b79e442abbe78af3954fb768c6d8562d5c853e288f14e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755259678565-75265394168690\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:08:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755259678565-75265394168690","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75265394168690","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-15T12:07:58.565Z"},"publishedAt":"2025-08-15T12:07:58.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:12:24
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:36]  Provided signature: sha256=926ad9645ff1ba24e3daf03304999d17a858157516a66270ebfa13f091dce6df
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:37]  Calculated signature: sha256=926ad9645ff1ba24e3daf03304999d17a858157516a66270ebfa13f091dce6df
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7de73f495457ecdb\n    [X-B3-Traceid] => 689f24252c345491658b69181e5af58f\n    [B3] => 689f24252c345491658b69181e5af58f-7de73f495457ecdb-1\n    [Traceparent] => 00-689f24252c345491658b69181e5af58f-7de73f495457ecdb-01\n    [X-Amzn-Trace-Id] => Root=1-689f2425-2c345491658b69181e5af58f;Parent=7de73f495457ecdb;Sampled=1\n    [X-Adsk-Signature] => sha256=926ad9645ff1ba24e3daf03304999d17a858157516a66270ebfa13f091dce6df\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755259941312-62521806254194\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755259941312-62521806254194","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62521806254194","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-15T12:12:21.312Z"},"publishedAt":"2025-08-15T12:12:21.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:12:24
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:36]  Provided signature: sha256=b33e270659d3c67435fd65c5f59be03b1d2813fc9c4c3936b14721962d61e937
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:37]  Calculated signature: sha256=926ad9645ff1ba24e3daf03304999d17a858157516a66270ebfa13f091dce6df
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f34a959c159a19d8\n    [X-B3-Traceid] => 689f24252c345491658b69181e5af58f\n    [B3] => 689f24252c345491658b69181e5af58f-f34a959c159a19d8-1\n    [Traceparent] => 00-689f24252c345491658b69181e5af58f-f34a959c159a19d8-01\n    [X-Amzn-Trace-Id] => Root=1-689f2425-2c345491658b69181e5af58f;Parent=f34a959c159a19d8;Sampled=1\n    [X-Adsk-Signature] => sha256=b33e270659d3c67435fd65c5f59be03b1d2813fc9c4c3936b14721962d61e937\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755259941312-62521806254194\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:12:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755259941312-62521806254194","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62521806254194","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-15T12:12:21.312Z"},"publishedAt":"2025-08-15T12:12:21.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:13:06
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:36]  Provided signature: sha256=1073b561fa5f669ac9f3e8914e233bca30a6d6294dd8266f3a0a0391980c4b05
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:37]  Calculated signature: sha256=22ff1ade38e30db2c4f62bea6be190eb7a34d9ee5618f4bd3d632f5c7d8723c8
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7f6416c68c66d6ab\n    [X-B3-Traceid] => 689f244fa8523699d0808b8cac16cdba\n    [B3] => 689f244fa8523699d0808b8cac16cdba-7f6416c68c66d6ab-1\n    [Traceparent] => 00-689f244fa8523699d0808b8cac16cdba-7f6416c68c66d6ab-01\n    [X-Amzn-Trace-Id] => Root=1-689f244f-a8523699d0808b8cac16cdba;Parent=7f6416c68c66d6ab;Sampled=1\n    [X-Adsk-Signature] => sha256=1073b561fa5f669ac9f3e8914e233bca30a6d6294dd8266f3a0a0391980c4b05\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 256eaf91-5492-4be9-ab30-b876b1a82a7a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"256eaf91-5492-4be9-ab30-b876b1a82a7a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008840","transactionId":"53e5c4bb-7c5a-5d16-8101-a5a25ad056c4","quoteStatus":"Draft","message":"Quote# Q-1008840 status changed to Draft.","modifiedAt":"2025-08-15T12:13:03.357Z"},"publishedAt":"2025-08-15T12:13:03.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:13:06
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:36]  Provided signature: sha256=22ff1ade38e30db2c4f62bea6be190eb7a34d9ee5618f4bd3d632f5c7d8723c8
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:37]  Calculated signature: sha256=22ff1ade38e30db2c4f62bea6be190eb7a34d9ee5618f4bd3d632f5c7d8723c8
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0a0a68842bca8dce\n    [X-B3-Traceid] => 689f244fa8523699d0808b8cac16cdba\n    [B3] => 689f244fa8523699d0808b8cac16cdba-0a0a68842bca8dce-1\n    [Traceparent] => 00-689f244fa8523699d0808b8cac16cdba-0a0a68842bca8dce-01\n    [X-Amzn-Trace-Id] => Root=1-689f244f-a8523699d0808b8cac16cdba;Parent=0a0a68842bca8dce;Sampled=1\n    [X-Adsk-Signature] => sha256=22ff1ade38e30db2c4f62bea6be190eb7a34d9ee5618f4bd3d632f5c7d8723c8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 256eaf91-5492-4be9-ab30-b876b1a82a7a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:13:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"256eaf91-5492-4be9-ab30-b876b1a82a7a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008840","transactionId":"53e5c4bb-7c5a-5d16-8101-a5a25ad056c4","quoteStatus":"Draft","message":"Quote# Q-1008840 status changed to Draft.","modifiedAt":"2025-08-15T12:13:03.357Z"},"publishedAt":"2025-08-15T12:13:03.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:15:15
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:36]  Provided signature: sha256=7396417273045b39552df7ba66a9124a5e29d8c517e56fd07a8c3a1a9bebe0ef
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:37]  Calculated signature: sha256=d087eb2a8385a858d6f6aa85ad15f331d65521bc91435416197602d05e67387d
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4ba68f13a138d61a\n    [X-B3-Traceid] => 689f24d1b1b2d88c3fda68d2d2d3ef51\n    [B3] => 689f24d1b1b2d88c3fda68d2d2d3ef51-4ba68f13a138d61a-1\n    [Traceparent] => 00-689f24d1b1b2d88c3fda68d2d2d3ef51-4ba68f13a138d61a-01\n    [X-Amzn-Trace-Id] => Root=1-689f24d1-b1b2d88c3fda68d2d2d3ef51;Parent=4ba68f13a138d61a;Sampled=1\n    [X-Adsk-Signature] => sha256=7396417273045b39552df7ba66a9124a5e29d8c517e56fd07a8c3a1a9bebe0ef\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => df6a7968-6a24-40c9-82e5-00f3b24f3263\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"df6a7968-6a24-40c9-82e5-00f3b24f3263","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008842","transactionId":"a1d45fd4-4607-5cc4-a27d-3ecd610789bc","quoteStatus":"Draft","message":"Quote# Q-1008842 status changed to Draft.","modifiedAt":"2025-08-15T12:15:12.933Z"},"publishedAt":"2025-08-15T12:15:13.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:15:15
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:36]  Provided signature: sha256=d087eb2a8385a858d6f6aa85ad15f331d65521bc91435416197602d05e67387d
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:37]  Calculated signature: sha256=d087eb2a8385a858d6f6aa85ad15f331d65521bc91435416197602d05e67387d
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e97784d99170f478\n    [X-B3-Traceid] => 689f24d1b1b2d88c3fda68d2d2d3ef51\n    [B3] => 689f24d1b1b2d88c3fda68d2d2d3ef51-e97784d99170f478-1\n    [Traceparent] => 00-689f24d1b1b2d88c3fda68d2d2d3ef51-e97784d99170f478-01\n    [X-Amzn-Trace-Id] => Root=1-689f24d1-b1b2d88c3fda68d2d2d3ef51;Parent=e97784d99170f478;Sampled=1\n    [X-Adsk-Signature] => sha256=d087eb2a8385a858d6f6aa85ad15f331d65521bc91435416197602d05e67387d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => df6a7968-6a24-40c9-82e5-00f3b24f3263\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:15:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"df6a7968-6a24-40c9-82e5-00f3b24f3263","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008842","transactionId":"a1d45fd4-4607-5cc4-a27d-3ecd610789bc","quoteStatus":"Draft","message":"Quote# Q-1008842 status changed to Draft.","modifiedAt":"2025-08-15T12:15:12.933Z"},"publishedAt":"2025-08-15T12:15:13.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:17:55
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:36]  Provided signature: sha256=3d39e6d1b6ff98296e68c9c0a303291af1411cc31c2569b8a98d23095f376dca
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:37]  Calculated signature: sha256=3d39e6d1b6ff98296e68c9c0a303291af1411cc31c2569b8a98d23095f376dca
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => da457289996c282b\n    [X-B3-Traceid] => 689f25714802aaf609af64e6299ca617\n    [B3] => 689f25714802aaf609af64e6299ca617-da457289996c282b-1\n    [Traceparent] => 00-689f25714802aaf609af64e6299ca617-da457289996c282b-01\n    [X-Amzn-Trace-Id] => Root=1-689f2571-4802aaf609af64e6299ca617;Parent=da457289996c282b;Sampled=1\n    [X-Adsk-Signature] => sha256=3d39e6d1b6ff98296e68c9c0a303291af1411cc31c2569b8a98d23095f376dca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755260273248-75265394172391\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755260273248-75265394172391","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75265394172391","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-15T12:17:53.248Z"},"publishedAt":"2025-08-15T12:17:53.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:17:55
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:36]  Provided signature: sha256=a625e4e607b3a8b0e1e26bd7147bc62b2d6b1f701abb53702b8051d57ac62ddd
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:37]  Calculated signature: sha256=3d39e6d1b6ff98296e68c9c0a303291af1411cc31c2569b8a98d23095f376dca
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d612946ba9d1e2fe\n    [X-B3-Traceid] => 689f25714802aaf609af64e6299ca617\n    [B3] => 689f25714802aaf609af64e6299ca617-d612946ba9d1e2fe-1\n    [Traceparent] => 00-689f25714802aaf609af64e6299ca617-d612946ba9d1e2fe-01\n    [X-Amzn-Trace-Id] => Root=1-689f2571-4802aaf609af64e6299ca617;Parent=d612946ba9d1e2fe;Sampled=1\n    [X-Adsk-Signature] => sha256=a625e4e607b3a8b0e1e26bd7147bc62b2d6b1f701abb53702b8051d57ac62ddd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755260273248-75265394172391\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:17:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755260273248-75265394172391","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75265394172391","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-15T12:17:53.248Z"},"publishedAt":"2025-08-15T12:17:53.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:32:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:32:44
[webhook] [2025-08-15 12:32:44] [adwsapi_v2.php:36]  Provided signature: sha256=4e201c43f5863c298490e548eb7e5e541dd32b5c20471da96451e95336fa29f3
[webhook] [2025-08-15 12:32:44] [adwsapi_v2.php:37]  Calculated signature: sha256=5253e399d7a62e298b51681e5995a066a055fa064e56407fc152bf200ec734c8
[webhook] [2025-08-15 12:32:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5c19d8c9bcd3135a\n    [X-B3-Traceid] => 689f28ea0c2523a77d93a9e9cff82a37\n    [B3] => 689f28ea0c2523a77d93a9e9cff82a37-5c19d8c9bcd3135a-1\n    [Traceparent] => 00-689f28ea0c2523a77d93a9e9cff82a37-5c19d8c9bcd3135a-01\n    [X-Amzn-Trace-Id] => Root=1-689f28ea-0c2523a77d93a9e9cff82a37;Parent=5c19d8c9bcd3135a;Sampled=1\n    [X-Adsk-Signature] => sha256=4e201c43f5863c298490e548eb7e5e541dd32b5c20471da96451e95336fa29f3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f030315e-cf82-4958-909f-0be7c762e595\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:32:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"f030315e-cf82-4958-909f-0be7c762e595","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008862","transactionId":"0df1ca79-dfea-5df3-b791-ab60f4f52469","quoteStatus":"Draft","message":"Quote# Q-1008862 status changed to Draft.","modifiedAt":"2025-08-15T12:32:42.025Z"},"publishedAt":"2025-08-15T12:32:42.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:32:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:32:45
[webhook] [2025-08-15 12:32:45] [adwsapi_v2.php:36]  Provided signature: sha256=5253e399d7a62e298b51681e5995a066a055fa064e56407fc152bf200ec734c8
[webhook] [2025-08-15 12:32:45] [adwsapi_v2.php:37]  Calculated signature: sha256=5253e399d7a62e298b51681e5995a066a055fa064e56407fc152bf200ec734c8
[webhook] [2025-08-15 12:32:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9609ce8bde83aa9e\n    [X-B3-Traceid] => 689f28ea0c2523a77d93a9e9cff82a37\n    [B3] => 689f28ea0c2523a77d93a9e9cff82a37-9609ce8bde83aa9e-1\n    [Traceparent] => 00-689f28ea0c2523a77d93a9e9cff82a37-9609ce8bde83aa9e-01\n    [X-Amzn-Trace-Id] => Root=1-689f28ea-0c2523a77d93a9e9cff82a37;Parent=9609ce8bde83aa9e;Sampled=1\n    [X-Adsk-Signature] => sha256=5253e399d7a62e298b51681e5995a066a055fa064e56407fc152bf200ec734c8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f030315e-cf82-4958-909f-0be7c762e595\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:32:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"f030315e-cf82-4958-909f-0be7c762e595","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008862","transactionId":"0df1ca79-dfea-5df3-b791-ab60f4f52469","quoteStatus":"Draft","message":"Quote# Q-1008862 status changed to Draft.","modifiedAt":"2025-08-15T12:32:42.025Z"},"publishedAt":"2025-08-15T12:32:42.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:34:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:34:54
[webhook] [2025-08-15 12:34:54] [adwsapi_v2.php:36]  Provided signature: sha256=9e152772cb44b9f9df4cc4fdc90ff8602e9594e89fa42dad85528e574dc6b344
[webhook] [2025-08-15 12:34:54] [adwsapi_v2.php:37]  Calculated signature: sha256=9e152772cb44b9f9df4cc4fdc90ff8602e9594e89fa42dad85528e574dc6b344
[webhook] [2025-08-15 12:34:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d4365599854b5e8d\n    [X-B3-Traceid] => 689f296c5ab8f080dee2a298cdc9bddb\n    [B3] => 689f296c5ab8f080dee2a298cdc9bddb-d4365599854b5e8d-1\n    [Traceparent] => 00-689f296c5ab8f080dee2a298cdc9bddb-d4365599854b5e8d-01\n    [X-Amzn-Trace-Id] => Root=1-689f296c-5ab8f080dee2a298cdc9bddb;Parent=d4365599854b5e8d;Sampled=1\n    [X-Adsk-Signature] => sha256=9e152772cb44b9f9df4cc4fdc90ff8602e9594e89fa42dad85528e574dc6b344\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5320ed6e-abbd-4e55-8e41-3d27e93e6aca\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:34:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"5320ed6e-abbd-4e55-8e41-3d27e93e6aca","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008862","transactionId":"0df1ca79-dfea-5df3-b791-ab60f4f52469","quoteStatus":"Quoted","message":"Quote# Q-1008862 status changed to Quoted.","modifiedAt":"2025-08-15T12:34:51.777Z"},"publishedAt":"2025-08-15T12:34:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:34:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:34:55
[webhook] [2025-08-15 12:34:55] [adwsapi_v2.php:36]  Provided signature: sha256=0d9c81bcf98b9998db80396e9a05072174cf61cd004a9c8f7aabd4c42ded8cb9
[webhook] [2025-08-15 12:34:55] [adwsapi_v2.php:37]  Calculated signature: sha256=9e152772cb44b9f9df4cc4fdc90ff8602e9594e89fa42dad85528e574dc6b344
[webhook] [2025-08-15 12:34:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e68ba3090223c838\n    [X-B3-Traceid] => 689f296c5ab8f080dee2a298cdc9bddb\n    [B3] => 689f296c5ab8f080dee2a298cdc9bddb-e68ba3090223c838-1\n    [Traceparent] => 00-689f296c5ab8f080dee2a298cdc9bddb-e68ba3090223c838-01\n    [X-Amzn-Trace-Id] => Root=1-689f296c-5ab8f080dee2a298cdc9bddb;Parent=e68ba3090223c838;Sampled=1\n    [X-Adsk-Signature] => sha256=0d9c81bcf98b9998db80396e9a05072174cf61cd004a9c8f7aabd4c42ded8cb9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5320ed6e-abbd-4e55-8e41-3d27e93e6aca\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:34:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"5320ed6e-abbd-4e55-8e41-3d27e93e6aca","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008862","transactionId":"0df1ca79-dfea-5df3-b791-ab60f4f52469","quoteStatus":"Quoted","message":"Quote# Q-1008862 status changed to Quoted.","modifiedAt":"2025-08-15T12:34:51.777Z"},"publishedAt":"2025-08-15T12:34:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:35:32
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:36]  Provided signature: sha256=ba9dda627a42ab7af926cab179971ec8d485e4321406f99d990b1c0116285b27
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:37]  Calculated signature: sha256=ba9dda627a42ab7af926cab179971ec8d485e4321406f99d990b1c0116285b27
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2ec40ecd4777edb7\n    [X-B3-Traceid] => 689f2991ae6abb403192f710d257dc08\n    [B3] => 689f2991ae6abb403192f710d257dc08-2ec40ecd4777edb7-1\n    [Traceparent] => 00-689f2991ae6abb403192f710d257dc08-2ec40ecd4777edb7-01\n    [X-Amzn-Trace-Id] => Root=1-689f2991-ae6abb403192f710d257dc08;Parent=2ec40ecd4777edb7;Sampled=1\n    [X-Adsk-Signature] => sha256=ba9dda627a42ab7af926cab179971ec8d485e4321406f99d990b1c0116285b27\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6e89d406-dd58-425f-9a83-d4c2483f123e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"6e89d406-dd58-425f-9a83-d4c2483f123e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008866","transactionId":"09e57328-ce64-558e-97bb-1231e5a2e86a","quoteStatus":"Draft","message":"Quote# Q-1008866 status changed to Draft.","modifiedAt":"2025-08-15T12:35:29.276Z"},"publishedAt":"2025-08-15T12:35:29.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:35:32
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:36]  Provided signature: sha256=104661819900179c2ccd51837dd0046dc6aa9ef78c726366f54abdcda8c7d1dd
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:37]  Calculated signature: sha256=ba9dda627a42ab7af926cab179971ec8d485e4321406f99d990b1c0116285b27
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 85581b2fd696014c\n    [X-B3-Traceid] => 689f2991ae6abb403192f710d257dc08\n    [B3] => 689f2991ae6abb403192f710d257dc08-85581b2fd696014c-1\n    [Traceparent] => 00-689f2991ae6abb403192f710d257dc08-85581b2fd696014c-01\n    [X-Amzn-Trace-Id] => Root=1-689f2991-ae6abb403192f710d257dc08;Parent=85581b2fd696014c;Sampled=1\n    [X-Adsk-Signature] => sha256=104661819900179c2ccd51837dd0046dc6aa9ef78c726366f54abdcda8c7d1dd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6e89d406-dd58-425f-9a83-d4c2483f123e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:35:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"6e89d406-dd58-425f-9a83-d4c2483f123e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008866","transactionId":"09e57328-ce64-558e-97bb-1231e5a2e86a","quoteStatus":"Draft","message":"Quote# Q-1008866 status changed to Draft.","modifiedAt":"2025-08-15T12:35:29.276Z"},"publishedAt":"2025-08-15T12:35:29.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:37:21
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:36]  Provided signature: sha256=14c5c973d7fee84159b8a85b713b25244ab7378add3e2b0e5abb5124be960496
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:37]  Calculated signature: sha256=43486f1ec8474a8d562230082e6340ded763be9712775f69e1c1c4f8337c02cb
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2f831897b11def6c\n    [X-B3-Traceid] => 689f29fe10c659676ec35d3cf07ad51f\n    [B3] => 689f29fe10c659676ec35d3cf07ad51f-2f831897b11def6c-1\n    [Traceparent] => 00-689f29fe10c659676ec35d3cf07ad51f-2f831897b11def6c-01\n    [X-Amzn-Trace-Id] => Root=1-689f29fe-10c659676ec35d3cf07ad51f;Parent=2f831897b11def6c;Sampled=1\n    [X-Adsk-Signature] => sha256=14c5c973d7fee84159b8a85b713b25244ab7378add3e2b0e5abb5124be960496\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 811a2d50-ff1b-4016-aed4-161cf1a93574\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"811a2d50-ff1b-4016-aed4-161cf1a93574","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Draft","message":"Quote# Q-1008871 status changed to Draft.","modifiedAt":"2025-08-15T12:37:18.766Z"},"publishedAt":"2025-08-15T12:37:19.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:37:21
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:36]  Provided signature: sha256=43486f1ec8474a8d562230082e6340ded763be9712775f69e1c1c4f8337c02cb
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:37]  Calculated signature: sha256=43486f1ec8474a8d562230082e6340ded763be9712775f69e1c1c4f8337c02cb
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7b2c33c586a58504\n    [X-B3-Traceid] => 689f29fe10c659676ec35d3cf07ad51f\n    [B3] => 689f29fe10c659676ec35d3cf07ad51f-7b2c33c586a58504-1\n    [Traceparent] => 00-689f29fe10c659676ec35d3cf07ad51f-7b2c33c586a58504-01\n    [X-Amzn-Trace-Id] => Root=1-689f29fe-10c659676ec35d3cf07ad51f;Parent=7b2c33c586a58504;Sampled=1\n    [X-Adsk-Signature] => sha256=43486f1ec8474a8d562230082e6340ded763be9712775f69e1c1c4f8337c02cb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 811a2d50-ff1b-4016-aed4-161cf1a93574\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:37:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"811a2d50-ff1b-4016-aed4-161cf1a93574","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Draft","message":"Quote# Q-1008871 status changed to Draft.","modifiedAt":"2025-08-15T12:37:18.766Z"},"publishedAt":"2025-08-15T12:37:19.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:38:42
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:36]  Provided signature: sha256=4616518cc53301403fde5a114b1163e99a7983d455f3cf04d384823ce08feebf
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:37]  Calculated signature: sha256=b017235dbfb4bb52b8a0c4289df576197d11186bcb1aec8a94fddc94cd4f0f68
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 291c3c1f932fb847\n    [X-B3-Traceid] => 689f2a4f49f130f7cbd1da50618e661e\n    [B3] => 689f2a4f49f130f7cbd1da50618e661e-291c3c1f932fb847-1\n    [Traceparent] => 00-689f2a4f49f130f7cbd1da50618e661e-291c3c1f932fb847-01\n    [X-Amzn-Trace-Id] => Root=1-689f2a4f-49f130f7cbd1da50618e661e;Parent=291c3c1f932fb847;Sampled=1\n    [X-Adsk-Signature] => sha256=4616518cc53301403fde5a114b1163e99a7983d455f3cf04d384823ce08feebf\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1c5dceb1-45db-409f-b9d1-d4364496c5e5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1c5dceb1-45db-409f-b9d1-d4364496c5e5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Quoted","message":"Quote# Q-1008871 status changed to Quoted.","modifiedAt":"2025-08-15T12:38:39.476Z"},"publishedAt":"2025-08-15T12:38:40.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:38:42
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:36]  Provided signature: sha256=b017235dbfb4bb52b8a0c4289df576197d11186bcb1aec8a94fddc94cd4f0f68
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:37]  Calculated signature: sha256=b017235dbfb4bb52b8a0c4289df576197d11186bcb1aec8a94fddc94cd4f0f68
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f50e6fd4def12a22\n    [X-B3-Traceid] => 689f2a4f49f130f7cbd1da50618e661e\n    [B3] => 689f2a4f49f130f7cbd1da50618e661e-f50e6fd4def12a22-1\n    [Traceparent] => 00-689f2a4f49f130f7cbd1da50618e661e-f50e6fd4def12a22-01\n    [X-Amzn-Trace-Id] => Root=1-689f2a4f-49f130f7cbd1da50618e661e;Parent=f50e6fd4def12a22;Sampled=1\n    [X-Adsk-Signature] => sha256=b017235dbfb4bb52b8a0c4289df576197d11186bcb1aec8a94fddc94cd4f0f68\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1c5dceb1-45db-409f-b9d1-d4364496c5e5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:38:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1c5dceb1-45db-409f-b9d1-d4364496c5e5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Quoted","message":"Quote# Q-1008871 status changed to Quoted.","modifiedAt":"2025-08-15T12:38:39.476Z"},"publishedAt":"2025-08-15T12:38:40.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:39:41
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:36]  Provided signature: sha256=c685a27d5bae3dfb57b6be4009ad64123bfa9e06c0ba860100e9337d8a327fef
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:37]  Calculated signature: sha256=c685a27d5bae3dfb57b6be4009ad64123bfa9e06c0ba860100e9337d8a327fef
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => dd38f0c6e3502b05\n    [X-B3-Traceid] => 689f2a8abccd41ca1762575bdea1ca65\n    [B3] => 689f2a8abccd41ca1762575bdea1ca65-dd38f0c6e3502b05-1\n    [Traceparent] => 00-689f2a8abccd41ca1762575bdea1ca65-dd38f0c6e3502b05-01\n    [X-Amzn-Trace-Id] => Root=1-689f2a8a-bccd41ca1762575bdea1ca65;Parent=dd38f0c6e3502b05;Sampled=1\n    [X-Adsk-Signature] => sha256=c685a27d5bae3dfb57b6be4009ad64123bfa9e06c0ba860100e9337d8a327fef\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b2a9c09c-364d-4eed-86fc-348fce473c64\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"b2a9c09c-364d-4eed-86fc-348fce473c64","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008873","transactionId":"d64fcc5c-9474-536f-a58b-ad45b8bcf165","quoteStatus":"Draft","message":"Quote# Q-1008873 status changed to Draft.","modifiedAt":"2025-08-15T12:39:38.040Z"},"publishedAt":"2025-08-15T12:39:38.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:39:41
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:36]  Provided signature: sha256=01fe70802dbad897e73e1312dbebac55ff1b6ed0bc4138779700caa5c3f198a8
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:37]  Calculated signature: sha256=c685a27d5bae3dfb57b6be4009ad64123bfa9e06c0ba860100e9337d8a327fef
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 24a2f6d5abbb96a1\n    [X-B3-Traceid] => 689f2a8abccd41ca1762575bdea1ca65\n    [B3] => 689f2a8abccd41ca1762575bdea1ca65-24a2f6d5abbb96a1-1\n    [Traceparent] => 00-689f2a8abccd41ca1762575bdea1ca65-24a2f6d5abbb96a1-01\n    [X-Amzn-Trace-Id] => Root=1-689f2a8a-bccd41ca1762575bdea1ca65;Parent=24a2f6d5abbb96a1;Sampled=1\n    [X-Adsk-Signature] => sha256=01fe70802dbad897e73e1312dbebac55ff1b6ed0bc4138779700caa5c3f198a8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b2a9c09c-364d-4eed-86fc-348fce473c64\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:39:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"b2a9c09c-364d-4eed-86fc-348fce473c64","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008873","transactionId":"d64fcc5c-9474-536f-a58b-ad45b8bcf165","quoteStatus":"Draft","message":"Quote# Q-1008873 status changed to Draft.","modifiedAt":"2025-08-15T12:39:38.040Z"},"publishedAt":"2025-08-15T12:39:38.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:40:50
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:36]  Provided signature: sha256=dd7943d5e92bedb18a8855f9f2886b110f8c0595725ea91272690a325007017c
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:37]  Calculated signature: sha256=dd7943d5e92bedb18a8855f9f2886b110f8c0595725ea91272690a325007017c
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b1294330899e2490\n    [X-B3-Traceid] => 689f2ad06fb3e66f38d618ce02467ada\n    [B3] => 689f2ad06fb3e66f38d618ce02467ada-b1294330899e2490-1\n    [Traceparent] => 00-689f2ad06fb3e66f38d618ce02467ada-b1294330899e2490-01\n    [X-Amzn-Trace-Id] => Root=1-689f2ad0-6fb3e66f38d618ce02467ada;Parent=b1294330899e2490;Sampled=1\n    [X-Adsk-Signature] => sha256=dd7943d5e92bedb18a8855f9f2886b110f8c0595725ea91272690a325007017c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 06638a2c-4457-4d4c-b603-d35dd6dccc1f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"06638a2c-4457-4d4c-b603-d35dd6dccc1f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008873","transactionId":"d64fcc5c-9474-536f-a58b-ad45b8bcf165","quoteStatus":"Quoted","message":"Quote# Q-1008873 status changed to Quoted.","modifiedAt":"2025-08-15T12:40:48.020Z"},"publishedAt":"2025-08-15T12:40:48.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:40:50
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:36]  Provided signature: sha256=c51af006aaf716bb073559fdb89d9bf8aba83dcea7f538804662bb4bfc9db8a1
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:37]  Calculated signature: sha256=dd7943d5e92bedb18a8855f9f2886b110f8c0595725ea91272690a325007017c
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 810a88bd1ecbd110\n    [X-B3-Traceid] => 689f2ad06fb3e66f38d618ce02467ada\n    [B3] => 689f2ad06fb3e66f38d618ce02467ada-810a88bd1ecbd110-1\n    [Traceparent] => 00-689f2ad06fb3e66f38d618ce02467ada-810a88bd1ecbd110-01\n    [X-Amzn-Trace-Id] => Root=1-689f2ad0-6fb3e66f38d618ce02467ada;Parent=810a88bd1ecbd110;Sampled=1\n    [X-Adsk-Signature] => sha256=c51af006aaf716bb073559fdb89d9bf8aba83dcea7f538804662bb4bfc9db8a1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 06638a2c-4457-4d4c-b603-d35dd6dccc1f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:40:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"06638a2c-4457-4d4c-b603-d35dd6dccc1f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008873","transactionId":"d64fcc5c-9474-536f-a58b-ad45b8bcf165","quoteStatus":"Quoted","message":"Quote# Q-1008873 status changed to Quoted.","modifiedAt":"2025-08-15T12:40:48.020Z"},"publishedAt":"2025-08-15T12:40:48.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:41:04
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:36]  Provided signature: sha256=dbe511bc7ca16721b9ff2c91f2d658a61cb05696e18d39fd615e285b6b7a7b8c
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:37]  Calculated signature: sha256=ca5cdfae07ce98b2af5f4a12c04b7bf962598fdeeab0e6f31fe4e82c48327b6d
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d6d03caafa889c5a\n    [X-B3-Traceid] => 689f2ade6c200eebe6b9c7d38b652805\n    [B3] => 689f2ade6c200eebe6b9c7d38b652805-d6d03caafa889c5a-1\n    [Traceparent] => 00-689f2ade6c200eebe6b9c7d38b652805-d6d03caafa889c5a-01\n    [X-Amzn-Trace-Id] => Root=1-689f2ade-6c200eebe6b9c7d38b652805;Parent=d6d03caafa889c5a;Sampled=1\n    [X-Adsk-Signature] => sha256=dbe511bc7ca16721b9ff2c91f2d658a61cb05696e18d39fd615e285b6b7a7b8c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c304d439-8ac3-4e2c-94a1-6b0f6a3325a6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"c304d439-8ac3-4e2c-94a1-6b0f6a3325a6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008876","transactionId":"f1e6afc8-2ad9-551d-96fa-ced107c8ceda","quoteStatus":"Draft","message":"Quote# Q-1008876 status changed to Draft.","modifiedAt":"2025-08-15T12:41:02.015Z"},"publishedAt":"2025-08-15T12:41:02.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:41:04
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:36]  Provided signature: sha256=ca5cdfae07ce98b2af5f4a12c04b7bf962598fdeeab0e6f31fe4e82c48327b6d
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:37]  Calculated signature: sha256=ca5cdfae07ce98b2af5f4a12c04b7bf962598fdeeab0e6f31fe4e82c48327b6d
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 340b90fcf2ed577a\n    [X-B3-Traceid] => 689f2ade6c200eebe6b9c7d38b652805\n    [B3] => 689f2ade6c200eebe6b9c7d38b652805-340b90fcf2ed577a-1\n    [Traceparent] => 00-689f2ade6c200eebe6b9c7d38b652805-340b90fcf2ed577a-01\n    [X-Amzn-Trace-Id] => Root=1-689f2ade-6c200eebe6b9c7d38b652805;Parent=340b90fcf2ed577a;Sampled=1\n    [X-Adsk-Signature] => sha256=ca5cdfae07ce98b2af5f4a12c04b7bf962598fdeeab0e6f31fe4e82c48327b6d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c304d439-8ac3-4e2c-94a1-6b0f6a3325a6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:41:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"c304d439-8ac3-4e2c-94a1-6b0f6a3325a6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008876","transactionId":"f1e6afc8-2ad9-551d-96fa-ced107c8ceda","quoteStatus":"Draft","message":"Quote# Q-1008876 status changed to Draft.","modifiedAt":"2025-08-15T12:41:02.015Z"},"publishedAt":"2025-08-15T12:41:02.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:42:14
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:36]  Provided signature: sha256=71ea5a59a4dcdcff9b8eb388080598b3a971604797ad2f54e927eb3242215847
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:37]  Calculated signature: sha256=6f298fde25eb460215d3dbb1813ee71924266a5c99c6ef7885a352172d8e21d1
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0cb15771134b81de\n    [X-B3-Traceid] => 689f2b239ef1d9e9022e9c3935942517\n    [B3] => 689f2b239ef1d9e9022e9c3935942517-0cb15771134b81de-1\n    [Traceparent] => 00-689f2b239ef1d9e9022e9c3935942517-0cb15771134b81de-01\n    [X-Amzn-Trace-Id] => Root=1-689f2b23-9ef1d9e9022e9c3935942517;Parent=0cb15771134b81de;Sampled=1\n    [X-Adsk-Signature] => sha256=71ea5a59a4dcdcff9b8eb388080598b3a971604797ad2f54e927eb3242215847\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 847613d9-57cc-4e12-b6bb-259376922099\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"847613d9-57cc-4e12-b6bb-259376922099","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008876","transactionId":"f1e6afc8-2ad9-551d-96fa-ced107c8ceda","quoteStatus":"Quoted","message":"Quote# Q-1008876 status changed to Quoted.","modifiedAt":"2025-08-15T12:42:11.714Z"},"publishedAt":"2025-08-15T12:42:12.000Z","csn":"5103159758"}
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 12:42:14
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:36]  Provided signature: sha256=6f298fde25eb460215d3dbb1813ee71924266a5c99c6ef7885a352172d8e21d1
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:37]  Calculated signature: sha256=6f298fde25eb460215d3dbb1813ee71924266a5c99c6ef7885a352172d8e21d1
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e41383b2e2724311\n    [X-B3-Traceid] => 689f2b239ef1d9e9022e9c3935942517\n    [B3] => 689f2b239ef1d9e9022e9c3935942517-e41383b2e2724311-1\n    [Traceparent] => 00-689f2b239ef1d9e9022e9c3935942517-e41383b2e2724311-01\n    [X-Amzn-Trace-Id] => Root=1-689f2b23-9ef1d9e9022e9c3935942517;Parent=e41383b2e2724311;Sampled=1\n    [X-Adsk-Signature] => sha256=6f298fde25eb460215d3dbb1813ee71924266a5c99c6ef7885a352172d8e21d1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 847613d9-57cc-4e12-b6bb-259376922099\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 12:42:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"847613d9-57cc-4e12-b6bb-259376922099","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008876","transactionId":"f1e6afc8-2ad9-551d-96fa-ced107c8ceda","quoteStatus":"Quoted","message":"Quote# Q-1008876 status changed to Quoted.","modifiedAt":"2025-08-15T12:42:11.714Z"},"publishedAt":"2025-08-15T12:42:12.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:02:02
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:02:02
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:36]  Provided signature: sha256=7559f7e6cb35e76da5adf3e6d862a22b550776223f635ed18afb77fbb95324b3
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:37]  Calculated signature: sha256=7559f7e6cb35e76da5adf3e6d862a22b550776223f635ed18afb77fbb95324b3
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f3b8f2e154f9ddde\n    [X-B3-Traceid] => 689f2fc71b6749eb2be7b276b03553d6\n    [B3] => 689f2fc71b6749eb2be7b276b03553d6-f3b8f2e154f9ddde-1\n    [Traceparent] => 00-689f2fc71b6749eb2be7b276b03553d6-f3b8f2e154f9ddde-01\n    [X-Amzn-Trace-Id] => Root=1-689f2fc7-1b6749eb2be7b276b03553d6;Parent=f3b8f2e154f9ddde;Sampled=1\n    [X-Adsk-Signature] => sha256=7559f7e6cb35e76da5adf3e6d862a22b550776223f635ed18afb77fbb95324b3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ce459fe4-91a4-41d7-8fe0-74330aa547e2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"ce459fe4-91a4-41d7-8fe0-74330aa547e2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008862","transactionId":"0df1ca79-dfea-5df3-b791-ab60f4f52469","quoteStatus":"Ordered","message":"Quote# Q-1008862 status changed to Ordered.","modifiedAt":"2025-08-15T13:01:59.305Z"},"publishedAt":"2025-08-15T13:01:59.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:36]  Provided signature: sha256=cbf26800b984f71c85bc705dd7c28b079601a8bdb228e31e920989399763f5fb
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:37]  Calculated signature: sha256=7559f7e6cb35e76da5adf3e6d862a22b550776223f635ed18afb77fbb95324b3
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ff1b86109333a39e\n    [X-B3-Traceid] => 689f2fc71b6749eb2be7b276b03553d6\n    [B3] => 689f2fc71b6749eb2be7b276b03553d6-ff1b86109333a39e-1\n    [Traceparent] => 00-689f2fc71b6749eb2be7b276b03553d6-ff1b86109333a39e-01\n    [X-Amzn-Trace-Id] => Root=1-689f2fc7-1b6749eb2be7b276b03553d6;Parent=ff1b86109333a39e;Sampled=1\n    [X-Adsk-Signature] => sha256=cbf26800b984f71c85bc705dd7c28b079601a8bdb228e31e920989399763f5fb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ce459fe4-91a4-41d7-8fe0-74330aa547e2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:02:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"ce459fe4-91a4-41d7-8fe0-74330aa547e2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008862","transactionId":"0df1ca79-dfea-5df3-b791-ab60f4f52469","quoteStatus":"Ordered","message":"Quote# Q-1008862 status changed to Ordered.","modifiedAt":"2025-08-15T13:01:59.305Z"},"publishedAt":"2025-08-15T13:01:59.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:03:48
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:36]  Provided signature: sha256=a5c80fef0b09ecb35b3a93d22884cf1bf175127aeef48c76d3be8ac3be8b1480
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:37]  Calculated signature: sha256=820b7542cf49b146db1ee32eff75a5213b8f71e4860132afcbc447825c988a2a
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 556e6a4990914878\n    [X-B3-Traceid] => 689f3031937848bd118e205364523e63\n    [B3] => 689f3031937848bd118e205364523e63-556e6a4990914878-1\n    [Traceparent] => 00-689f3031937848bd118e205364523e63-556e6a4990914878-01\n    [X-Amzn-Trace-Id] => Root=1-689f3031-937848bd118e205364523e63;Parent=556e6a4990914878;Sampled=1\n    [X-Adsk-Signature] => sha256=a5c80fef0b09ecb35b3a93d22884cf1bf175127aeef48c76d3be8ac3be8b1480\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1919c8ac-cf2a-4b3c-86b6-fa7aa540727e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1919c8ac-cf2a-4b3c-86b6-fa7aa540727e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Order Submitted","message":"Quote# Q-1008871 status changed to Order Submitted.","modifiedAt":"2025-08-15T13:03:45.269Z"},"publishedAt":"2025-08-15T13:03:45.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:03:48
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:36]  Provided signature: sha256=820b7542cf49b146db1ee32eff75a5213b8f71e4860132afcbc447825c988a2a
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:37]  Calculated signature: sha256=820b7542cf49b146db1ee32eff75a5213b8f71e4860132afcbc447825c988a2a
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e0fc47167289b362\n    [X-B3-Traceid] => 689f3031937848bd118e205364523e63\n    [B3] => 689f3031937848bd118e205364523e63-e0fc47167289b362-1\n    [Traceparent] => 00-689f3031937848bd118e205364523e63-e0fc47167289b362-01\n    [X-Amzn-Trace-Id] => Root=1-689f3031-937848bd118e205364523e63;Parent=e0fc47167289b362;Sampled=1\n    [X-Adsk-Signature] => sha256=820b7542cf49b146db1ee32eff75a5213b8f71e4860132afcbc447825c988a2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1919c8ac-cf2a-4b3c-86b6-fa7aa540727e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:03:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1919c8ac-cf2a-4b3c-86b6-fa7aa540727e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Order Submitted","message":"Quote# Q-1008871 status changed to Order Submitted.","modifiedAt":"2025-08-15T13:03:45.269Z"},"publishedAt":"2025-08-15T13:03:45.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:03:49
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:36]  Provided signature: sha256=6e20c8a9e1cf94215214c7ddedeee93f9a0c18ae0ba4aef908852ac35c06e496
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:37]  Calculated signature: sha256=b177fcb913c5a7fa8392d0ac8d91e68c48d333643dadb23f6543cf0b759f6b3b
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 422db45a94cf1fda\n    [X-B3-Traceid] => 689f30322b5fd47fd1ca5f338f24f770\n    [B3] => 689f30322b5fd47fd1ca5f338f24f770-422db45a94cf1fda-1\n    [Traceparent] => 00-689f30322b5fd47fd1ca5f338f24f770-422db45a94cf1fda-01\n    [X-Amzn-Trace-Id] => Root=1-689f3032-2b5fd47fd1ca5f338f24f770;Parent=422db45a94cf1fda;Sampled=1\n    [X-Adsk-Signature] => sha256=6e20c8a9e1cf94215214c7ddedeee93f9a0c18ae0ba4aef908852ac35c06e496\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b78485a9-8105-424e-9094-e85150e7fae0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"b78485a9-8105-424e-9094-e85150e7fae0","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Ordered","message":"Quote# Q-1008871 status changed to Ordered.","modifiedAt":"2025-08-15T13:03:46.752Z"},"publishedAt":"2025-08-15T13:03:47.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:03:49
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:36]  Provided signature: sha256=b177fcb913c5a7fa8392d0ac8d91e68c48d333643dadb23f6543cf0b759f6b3b
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:37]  Calculated signature: sha256=b177fcb913c5a7fa8392d0ac8d91e68c48d333643dadb23f6543cf0b759f6b3b
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 325ab27b4db0338c\n    [X-B3-Traceid] => 689f30322b5fd47fd1ca5f338f24f770\n    [B3] => 689f30322b5fd47fd1ca5f338f24f770-325ab27b4db0338c-1\n    [Traceparent] => 00-689f30322b5fd47fd1ca5f338f24f770-325ab27b4db0338c-01\n    [X-Amzn-Trace-Id] => Root=1-689f3032-2b5fd47fd1ca5f338f24f770;Parent=325ab27b4db0338c;Sampled=1\n    [X-Adsk-Signature] => sha256=b177fcb913c5a7fa8392d0ac8d91e68c48d333643dadb23f6543cf0b759f6b3b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b78485a9-8105-424e-9094-e85150e7fae0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:03:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"b78485a9-8105-424e-9094-e85150e7fae0","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008871","transactionId":"8df5af2b-a328-5a0b-80c9-ce328f1bb774","quoteStatus":"Ordered","message":"Quote# Q-1008871 status changed to Ordered.","modifiedAt":"2025-08-15T13:03:46.752Z"},"publishedAt":"2025-08-15T13:03:47.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:56:44
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:36]  Provided signature: sha256=a913c3252c667a8430eb1098329cdfba952a31915ae9f2c5307f5888c032bc64
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:37]  Calculated signature: sha256=81c09b32dc200a81856a834ecd606e54da929d2a047d0b5ce6a1c83194a3993c
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9666c12b6d6cf5cb\n    [X-B3-Traceid] => 689f3c9a79764ba416db915488757b31\n    [B3] => 689f3c9a79764ba416db915488757b31-9666c12b6d6cf5cb-1\n    [Traceparent] => 00-689f3c9a79764ba416db915488757b31-9666c12b6d6cf5cb-01\n    [X-Amzn-Trace-Id] => Root=1-689f3c9a-79764ba416db915488757b31;Parent=9666c12b6d6cf5cb;Sampled=1\n    [X-Adsk-Signature] => sha256=a913c3252c667a8430eb1098329cdfba952a31915ae9f2c5307f5888c032bc64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 50529b1c-8a52-4ece-ad3f-86af2c6b65a2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"50529b1c-8a52-4ece-ad3f-86af2c6b65a2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1009008","transactionId":"f0e009d3-7c9d-57ca-ac76-68ca4253d1e2","quoteStatus":"Draft","message":"Quote# Q-1009008 status changed to Draft.","modifiedAt":"2025-08-15T13:56:42.181Z"},"publishedAt":"2025-08-15T13:56:42.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:56:44
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:36]  Provided signature: sha256=81c09b32dc200a81856a834ecd606e54da929d2a047d0b5ce6a1c83194a3993c
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:37]  Calculated signature: sha256=81c09b32dc200a81856a834ecd606e54da929d2a047d0b5ce6a1c83194a3993c
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ce405ac7317cf823\n    [X-B3-Traceid] => 689f3c9a79764ba416db915488757b31\n    [B3] => 689f3c9a79764ba416db915488757b31-ce405ac7317cf823-1\n    [Traceparent] => 00-689f3c9a79764ba416db915488757b31-ce405ac7317cf823-01\n    [X-Amzn-Trace-Id] => Root=1-689f3c9a-79764ba416db915488757b31;Parent=ce405ac7317cf823;Sampled=1\n    [X-Adsk-Signature] => sha256=81c09b32dc200a81856a834ecd606e54da929d2a047d0b5ce6a1c83194a3993c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 50529b1c-8a52-4ece-ad3f-86af2c6b65a2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:56:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"50529b1c-8a52-4ece-ad3f-86af2c6b65a2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1009008","transactionId":"f0e009d3-7c9d-57ca-ac76-68ca4253d1e2","quoteStatus":"Draft","message":"Quote# Q-1009008 status changed to Draft.","modifiedAt":"2025-08-15T13:56:42.181Z"},"publishedAt":"2025-08-15T13:56:42.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:57:17
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:36]  Provided signature: sha256=43f76c8d45eb95e46a16ea93528d29f21547ae4173042e159c67607124340c72
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:37]  Calculated signature: sha256=43f76c8d45eb95e46a16ea93528d29f21547ae4173042e159c67607124340c72
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fd336ed2aa6a5693\n    [X-B3-Traceid] => 689f3cba841b55164a7457ed80f6ec6b\n    [B3] => 689f3cba841b55164a7457ed80f6ec6b-fd336ed2aa6a5693-1\n    [Traceparent] => 00-689f3cba841b55164a7457ed80f6ec6b-fd336ed2aa6a5693-01\n    [X-Amzn-Trace-Id] => Root=1-689f3cba-841b55164a7457ed80f6ec6b;Parent=fd336ed2aa6a5693;Sampled=1\n    [X-Adsk-Signature] => sha256=43f76c8d45eb95e46a16ea93528d29f21547ae4173042e159c67607124340c72\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dea67d9e-9ea1-43dd-9081-d176b8dc03cd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"dea67d9e-9ea1-43dd-9081-d176b8dc03cd","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1009008","transactionId":"f0e009d3-7c9d-57ca-ac76-68ca4253d1e2","quoteStatus":"Quoted","message":"Quote# Q-1009008 status changed to Quoted.","modifiedAt":"2025-08-15T13:57:14.325Z"},"publishedAt":"2025-08-15T13:57:14.000Z","csn":"5103159758"}
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 13:57:17
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:36]  Provided signature: sha256=a97fd4eac9decd30babe811880c62df51dbe1736f657571c432e275878152419
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:37]  Calculated signature: sha256=43f76c8d45eb95e46a16ea93528d29f21547ae4173042e159c67607124340c72
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 774f1813a4f67002\n    [X-B3-Traceid] => 689f3cba841b55164a7457ed80f6ec6b\n    [B3] => 689f3cba841b55164a7457ed80f6ec6b-774f1813a4f67002-1\n    [Traceparent] => 00-689f3cba841b55164a7457ed80f6ec6b-774f1813a4f67002-01\n    [X-Amzn-Trace-Id] => Root=1-689f3cba-841b55164a7457ed80f6ec6b;Parent=774f1813a4f67002;Sampled=1\n    [X-Adsk-Signature] => sha256=a97fd4eac9decd30babe811880c62df51dbe1736f657571c432e275878152419\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dea67d9e-9ea1-43dd-9081-d176b8dc03cd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 13:57:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"dea67d9e-9ea1-43dd-9081-d176b8dc03cd","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1009008","transactionId":"f0e009d3-7c9d-57ca-ac76-68ca4253d1e2","quoteStatus":"Quoted","message":"Quote# Q-1009008 status changed to Quoted.","modifiedAt":"2025-08-15T13:57:14.325Z"},"publishedAt":"2025-08-15T13:57:14.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:01:02
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:36]  Provided signature: sha256=cd84c08b6a033f5caf3a3f058e2f0bf8c98ec84089ed3ab0981aa3df148aeea5
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:37]  Calculated signature: sha256=42bd012596249a22d7162838d556eeedb14f2c4c2e41bdff6a699c3c424ada99
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 26997f06de2fdcc5\n    [X-B3-Traceid] => 689fbc2b3ffad26e3acf16910f2d4235\n    [B3] => 689fbc2b3ffad26e3acf16910f2d4235-26997f06de2fdcc5-1\n    [Traceparent] => 00-689fbc2b3ffad26e3acf16910f2d4235-26997f06de2fdcc5-01\n    [X-Amzn-Trace-Id] => Root=1-689fbc2b-3ffad26e3acf16910f2d4235;Parent=26997f06de2fdcc5;Sampled=1\n    [X-Adsk-Signature] => sha256=cd84c08b6a033f5caf3a3f058e2f0bf8c98ec84089ed3ab0981aa3df148aeea5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 80d9da77-c140-4f55-b373-3eb7b5f2f408\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"80d9da77-c140-4f55-b373-3eb7b5f2f408","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-936077","transactionId":"968ff399-bb5d-518d-a445-93681b69a0e4","quoteStatus":"Expired","message":"Quote# Q-936077 status changed to Expired.","modifiedAt":"2025-08-15T23:00:50.464Z"},"publishedAt":"2025-08-15T23:00:59.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:01:02
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:36]  Provided signature: sha256=42bd012596249a22d7162838d556eeedb14f2c4c2e41bdff6a699c3c424ada99
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:37]  Calculated signature: sha256=42bd012596249a22d7162838d556eeedb14f2c4c2e41bdff6a699c3c424ada99
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 02f0a00c4528cf42\n    [X-B3-Traceid] => 689fbc2b3ffad26e3acf16910f2d4235\n    [B3] => 689fbc2b3ffad26e3acf16910f2d4235-02f0a00c4528cf42-1\n    [Traceparent] => 00-689fbc2b3ffad26e3acf16910f2d4235-02f0a00c4528cf42-01\n    [X-Amzn-Trace-Id] => Root=1-689fbc2b-3ffad26e3acf16910f2d4235;Parent=02f0a00c4528cf42;Sampled=1\n    [X-Adsk-Signature] => sha256=42bd012596249a22d7162838d556eeedb14f2c4c2e41bdff6a699c3c424ada99\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 80d9da77-c140-4f55-b373-3eb7b5f2f408\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:01:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"80d9da77-c140-4f55-b373-3eb7b5f2f408","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-936077","transactionId":"968ff399-bb5d-518d-a445-93681b69a0e4","quoteStatus":"Expired","message":"Quote# Q-936077 status changed to Expired.","modifiedAt":"2025-08-15T23:00:50.464Z"},"publishedAt":"2025-08-15T23:00:59.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:02:19
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:36]  Provided signature: sha256=d1b73f0fcce474c0d26c2acedea44c6c515cc27525b3764291db564673dd7c10
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:37]  Calculated signature: sha256=b3267465cfdf16eef45c6b91eef37beead351b563a2c1654f32f86baa8e30733
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => eac3aa4ef63f37fe\n    [X-B3-Traceid] => 689fbc79275de87e7ffa2de15c07bdfb\n    [B3] => 689fbc79275de87e7ffa2de15c07bdfb-eac3aa4ef63f37fe-1\n    [Traceparent] => 00-689fbc79275de87e7ffa2de15c07bdfb-eac3aa4ef63f37fe-01\n    [X-Amzn-Trace-Id] => Root=1-689fbc79-275de87e7ffa2de15c07bdfb;Parent=eac3aa4ef63f37fe;Sampled=1\n    [X-Adsk-Signature] => sha256=d1b73f0fcce474c0d26c2acedea44c6c515cc27525b3764291db564673dd7c10\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755298937045-574-12406719\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755298937045-574-12406719","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-12406719","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-15T23:02:17.045Z"},"publishedAt":"2025-08-15T23:02:17.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:02:19
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:36]  Provided signature: sha256=b3267465cfdf16eef45c6b91eef37beead351b563a2c1654f32f86baa8e30733
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:37]  Calculated signature: sha256=b3267465cfdf16eef45c6b91eef37beead351b563a2c1654f32f86baa8e30733
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f8519b89fd3696cd\n    [X-B3-Traceid] => 689fbc79275de87e7ffa2de15c07bdfb\n    [B3] => 689fbc79275de87e7ffa2de15c07bdfb-f8519b89fd3696cd-1\n    [Traceparent] => 00-689fbc79275de87e7ffa2de15c07bdfb-f8519b89fd3696cd-01\n    [X-Amzn-Trace-Id] => Root=1-689fbc79-275de87e7ffa2de15c07bdfb;Parent=f8519b89fd3696cd;Sampled=1\n    [X-Adsk-Signature] => sha256=b3267465cfdf16eef45c6b91eef37beead351b563a2c1654f32f86baa8e30733\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755298937045-574-12406719\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:02:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755298937045-574-12406719","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-12406719","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-15T23:02:17.045Z"},"publishedAt":"2025-08-15T23:02:17.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:54
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:36]  Provided signature: sha256=4e2257d339b3093b68b557a37a206aea3c617bbd0de7523594e998342b8a1360
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:37]  Calculated signature: sha256=4bbdd2d4331e236ceadde516cd6752f6e8e07ae2b7f8c4c91cb30772fd2d1e13
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f9a8af65103db5ee\n    [X-B3-Traceid] => 689fc5480e4c451c1709b83c7c9fb1c3\n    [B3] => 689fc5480e4c451c1709b83c7c9fb1c3-f9a8af65103db5ee-1\n    [Traceparent] => 00-689fc5480e4c451c1709b83c7c9fb1c3-f9a8af65103db5ee-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-0e4c451c1709b83c7c9fb1c3;Parent=f9a8af65103db5ee;Sampled=1\n    [X-Adsk-Signature] => sha256=4e2257d339b3093b68b557a37a206aea3c617bbd0de7523594e998342b8a1360\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0ffd5d84-499c-48ae-ac1b-808096088ed2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"0ffd5d84-499c-48ae-ac1b-808096088ed2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291637956","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:54
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:36]  Provided signature: sha256=c2b7057d1be3ce855928dcafe653ea95d60ec113c01872d42a2adb00cbdb0ecb
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:37]  Calculated signature: sha256=696a1cd33791a93d21e7b930bd5b98d42cd12174430fbf24c8c387318deee63e
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4b3fb1a4825d1932\n    [X-B3-Traceid] => 689fc5480349aee2112f4fb6566f1136\n    [B3] => 689fc5480349aee2112f4fb6566f1136-4b3fb1a4825d1932-1\n    [Traceparent] => 00-689fc5480349aee2112f4fb6566f1136-4b3fb1a4825d1932-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-0349aee2112f4fb6566f1136;Parent=4b3fb1a4825d1932;Sampled=1\n    [X-Adsk-Signature] => sha256=c2b7057d1be3ce855928dcafe653ea95d60ec113c01872d42a2adb00cbdb0ecb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3b98e147-bf09-48f1-8c4e-8ae948f3c4c1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"3b98e147-bf09-48f1-8c4e-8ae948f3c4c1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302263912","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:54
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:36]  Provided signature: sha256=7f1d6e68a812c1f26abba2f003d68fa59ba3b3e37e1fd7d7a3fafe077a7bd9f6
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:37]  Calculated signature: sha256=7f1d6e68a812c1f26abba2f003d68fa59ba3b3e37e1fd7d7a3fafe077a7bd9f6
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bab90c3759410381\n    [X-B3-Traceid] => 689fc548123744804a461fc73db9c0f7\n    [B3] => 689fc548123744804a461fc73db9c0f7-bab90c3759410381-1\n    [Traceparent] => 00-689fc548123744804a461fc73db9c0f7-bab90c3759410381-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-123744804a461fc73db9c0f7;Parent=bab90c3759410381;Sampled=1\n    [X-Adsk-Signature] => sha256=7f1d6e68a812c1f26abba2f003d68fa59ba3b3e37e1fd7d7a3fafe077a7bd9f6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cb0a9528-7041-41c7-8c93-987ecdbfb57d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"cb0a9528-7041-41c7-8c93-987ecdbfb57d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302267713","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:54
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:36]  Provided signature: sha256=4bbdd2d4331e236ceadde516cd6752f6e8e07ae2b7f8c4c91cb30772fd2d1e13
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:37]  Calculated signature: sha256=4bbdd2d4331e236ceadde516cd6752f6e8e07ae2b7f8c4c91cb30772fd2d1e13
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bb4ea7005582f33c\n    [X-B3-Traceid] => 689fc5480e4c451c1709b83c7c9fb1c3\n    [B3] => 689fc5480e4c451c1709b83c7c9fb1c3-bb4ea7005582f33c-1\n    [Traceparent] => 00-689fc5480e4c451c1709b83c7c9fb1c3-bb4ea7005582f33c-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-0e4c451c1709b83c7c9fb1c3;Parent=bb4ea7005582f33c;Sampled=1\n    [X-Adsk-Signature] => sha256=4bbdd2d4331e236ceadde516cd6752f6e8e07ae2b7f8c4c91cb30772fd2d1e13\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0ffd5d84-499c-48ae-ac1b-808096088ed2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"0ffd5d84-499c-48ae-ac1b-808096088ed2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291637956","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:54
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:36]  Provided signature: sha256=216eeb2e3d20a55dcb420a4f7cce7a8df69bb4b67d0cb28f410136448fc399c3
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:37]  Calculated signature: sha256=7f1d6e68a812c1f26abba2f003d68fa59ba3b3e37e1fd7d7a3fafe077a7bd9f6
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ec9d5cf26f8f81e8\n    [X-B3-Traceid] => 689fc548123744804a461fc73db9c0f7\n    [B3] => 689fc548123744804a461fc73db9c0f7-ec9d5cf26f8f81e8-1\n    [Traceparent] => 00-689fc548123744804a461fc73db9c0f7-ec9d5cf26f8f81e8-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-123744804a461fc73db9c0f7;Parent=ec9d5cf26f8f81e8;Sampled=1\n    [X-Adsk-Signature] => sha256=216eeb2e3d20a55dcb420a4f7cce7a8df69bb4b67d0cb28f410136448fc399c3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cb0a9528-7041-41c7-8c93-987ecdbfb57d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"cb0a9528-7041-41c7-8c93-987ecdbfb57d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302267713","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:54
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:36]  Provided signature: sha256=696a1cd33791a93d21e7b930bd5b98d42cd12174430fbf24c8c387318deee63e
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:37]  Calculated signature: sha256=696a1cd33791a93d21e7b930bd5b98d42cd12174430fbf24c8c387318deee63e
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b0e075f9a0cef9cf\n    [X-B3-Traceid] => 689fc5480349aee2112f4fb6566f1136\n    [B3] => 689fc5480349aee2112f4fb6566f1136-b0e075f9a0cef9cf-1\n    [Traceparent] => 00-689fc5480349aee2112f4fb6566f1136-b0e075f9a0cef9cf-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-0349aee2112f4fb6566f1136;Parent=b0e075f9a0cef9cf;Sampled=1\n    [X-Adsk-Signature] => sha256=696a1cd33791a93d21e7b930bd5b98d42cd12174430fbf24c8c387318deee63e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3b98e147-bf09-48f1-8c4e-8ae948f3c4c1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"3b98e147-bf09-48f1-8c4e-8ae948f3c4c1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302263912","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:54
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:36]  Provided signature: sha256=c5dbbda5c6db9b4844c54ff7b23d5de1b066207523bb88afdd37499fc6e7f054
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:37]  Calculated signature: sha256=f845b3229039f20a1ec668522cde04efcabe790fec80e7d4769326f9f722674e
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => da8108409d1d7037\n    [X-B3-Traceid] => 689fc548464165c90282dd5141ee474b\n    [B3] => 689fc548464165c90282dd5141ee474b-da8108409d1d7037-1\n    [Traceparent] => 00-689fc548464165c90282dd5141ee474b-da8108409d1d7037-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-464165c90282dd5141ee474b;Parent=da8108409d1d7037;Sampled=1\n    [X-Adsk-Signature] => sha256=c5dbbda5c6db9b4844c54ff7b23d5de1b066207523bb88afdd37499fc6e7f054\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f731c9f7-a2e1-462b-ac86-496995c72a45\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"f731c9f7-a2e1-462b-ac86-496995c72a45","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291633555","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-15 23:39:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-15 23:39:55
[webhook] [2025-08-15 23:39:55] [adwsapi_v2.php:36]  Provided signature: sha256=f845b3229039f20a1ec668522cde04efcabe790fec80e7d4769326f9f722674e
[webhook] [2025-08-15 23:39:55] [adwsapi_v2.php:37]  Calculated signature: sha256=f845b3229039f20a1ec668522cde04efcabe790fec80e7d4769326f9f722674e
[webhook] [2025-08-15 23:39:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7c82e4118ae78d94\n    [X-B3-Traceid] => 689fc548464165c90282dd5141ee474b\n    [B3] => 689fc548464165c90282dd5141ee474b-7c82e4118ae78d94-1\n    [Traceparent] => 00-689fc548464165c90282dd5141ee474b-7c82e4118ae78d94-01\n    [X-Amzn-Trace-Id] => Root=1-689fc548-464165c90282dd5141ee474b;Parent=7c82e4118ae78d94;Sampled=1\n    [X-Adsk-Signature] => sha256=f845b3229039f20a1ec668522cde04efcabe790fec80e7d4769326f9f722674e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f731c9f7-a2e1-462b-ac86-496995c72a45\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-15 23:39:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"f731c9f7-a2e1-462b-ac86-496995c72a45","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291633555","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-15T23:24:47.000+0000"},"publishedAt":"2025-08-15T23:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:05:28
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:36]  Provided signature: sha256=2fe72f621ee0f6d0cf2b86a24759a41b5f079dec8e0012ecc6b5b05d09845e42
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:37]  Calculated signature: sha256=2fe72f621ee0f6d0cf2b86a24759a41b5f079dec8e0012ecc6b5b05d09845e42
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 01ae72d1f1c0d995\n    [X-B3-Traceid] => 68a07406261befc978b6b838772694ee\n    [B3] => 68a07406261befc978b6b838772694ee-01ae72d1f1c0d995-1\n    [Traceparent] => 00-68a07406261befc978b6b838772694ee-01ae72d1f1c0d995-01\n    [X-Amzn-Trace-Id] => Root=1-68a07406-261befc978b6b838772694ee;Parent=01ae72d1f1c0d995;Sampled=1\n    [X-Adsk-Signature] => sha256=2fe72f621ee0f6d0cf2b86a24759a41b5f079dec8e0012ecc6b5b05d09845e42\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755345926235-75525391314639\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755345926235-75525391314639","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75525391314639","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:05:26.235Z"},"publishedAt":"2025-08-16T12:05:26.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:05:28
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:36]  Provided signature: sha256=910af48678ed1f8eee211c6d4137bab465a7b6dd76c40d00b9ffb6cd1ab1d8ee
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:37]  Calculated signature: sha256=2fe72f621ee0f6d0cf2b86a24759a41b5f079dec8e0012ecc6b5b05d09845e42
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 47cec418c0881c39\n    [X-B3-Traceid] => 68a07406261befc978b6b838772694ee\n    [B3] => 68a07406261befc978b6b838772694ee-47cec418c0881c39-1\n    [Traceparent] => 00-68a07406261befc978b6b838772694ee-47cec418c0881c39-01\n    [X-Amzn-Trace-Id] => Root=1-68a07406-261befc978b6b838772694ee;Parent=47cec418c0881c39;Sampled=1\n    [X-Adsk-Signature] => sha256=910af48678ed1f8eee211c6d4137bab465a7b6dd76c40d00b9ffb6cd1ab1d8ee\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755345926235-75525391314639\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:05:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755345926235-75525391314639","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75525391314639","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:05:26.235Z"},"publishedAt":"2025-08-16T12:05:26.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:15:16
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:36]  Provided signature: sha256=3158c33e15f6fe46b7fdb37d430f2b50fb97452123c6e70e4b13bd8507f300a3
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:37]  Calculated signature: sha256=8e96ea63fb3a1a65deab91fb93776ecc89c1f139cde5c7a6818a36d291fc6759
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8a02334b80d50d3b\n    [X-B3-Traceid] => 68a076526859fe767eae00a12d17df02\n    [B3] => 68a076526859fe767eae00a12d17df02-8a02334b80d50d3b-1\n    [Traceparent] => 00-68a076526859fe767eae00a12d17df02-8a02334b80d50d3b-01\n    [X-Amzn-Trace-Id] => Root=1-68a07652-6859fe767eae00a12d17df02;Parent=8a02334b80d50d3b;Sampled=1\n    [X-Adsk-Signature] => sha256=3158c33e15f6fe46b7fdb37d430f2b50fb97452123c6e70e4b13bd8507f300a3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346514467-75526302263912\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346514467-75526302263912","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302263912","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:15:14.467Z"},"publishedAt":"2025-08-16T12:15:14.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:15:16
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:36]  Provided signature: sha256=8e96ea63fb3a1a65deab91fb93776ecc89c1f139cde5c7a6818a36d291fc6759
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:37]  Calculated signature: sha256=8e96ea63fb3a1a65deab91fb93776ecc89c1f139cde5c7a6818a36d291fc6759
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e9fcdd519ef6066c\n    [X-B3-Traceid] => 68a076526859fe767eae00a12d17df02\n    [B3] => 68a076526859fe767eae00a12d17df02-e9fcdd519ef6066c-1\n    [Traceparent] => 00-68a076526859fe767eae00a12d17df02-e9fcdd519ef6066c-01\n    [X-Amzn-Trace-Id] => Root=1-68a07652-6859fe767eae00a12d17df02;Parent=e9fcdd519ef6066c;Sampled=1\n    [X-Adsk-Signature] => sha256=8e96ea63fb3a1a65deab91fb93776ecc89c1f139cde5c7a6818a36d291fc6759\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346514467-75526302263912\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:15:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346514467-75526302263912","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302263912","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:15:14.467Z"},"publishedAt":"2025-08-16T12:15:14.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:15:18
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:36]  Provided signature: sha256=52e8d0163dbd1b25e1d038c7cea4938910f3b8a0b18f3f5d4179ecb6ae516ab7
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:37]  Calculated signature: sha256=4217ca4f77c332dcf4596030e7d0d88e20406799a1262c12767c2f48da825131
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8289c7ea8c0f48d3\n    [X-B3-Traceid] => 68a076546f4d8c466206db7f04ee56de\n    [B3] => 68a076546f4d8c466206db7f04ee56de-8289c7ea8c0f48d3-1\n    [Traceparent] => 00-68a076546f4d8c466206db7f04ee56de-8289c7ea8c0f48d3-01\n    [X-Amzn-Trace-Id] => Root=1-68a07654-6f4d8c466206db7f04ee56de;Parent=8289c7ea8c0f48d3;Sampled=1\n    [X-Adsk-Signature] => sha256=52e8d0163dbd1b25e1d038c7cea4938910f3b8a0b18f3f5d4179ecb6ae516ab7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346516310-75526291637956\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346516310-75526291637956","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291637956","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:15:16.310Z"},"publishedAt":"2025-08-16T12:15:16.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:15:18
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:36]  Provided signature: sha256=4217ca4f77c332dcf4596030e7d0d88e20406799a1262c12767c2f48da825131
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:37]  Calculated signature: sha256=4217ca4f77c332dcf4596030e7d0d88e20406799a1262c12767c2f48da825131
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a4f932ba78c8de99\n    [X-B3-Traceid] => 68a076546f4d8c466206db7f04ee56de\n    [B3] => 68a076546f4d8c466206db7f04ee56de-a4f932ba78c8de99-1\n    [Traceparent] => 00-68a076546f4d8c466206db7f04ee56de-a4f932ba78c8de99-01\n    [X-Amzn-Trace-Id] => Root=1-68a07654-6f4d8c466206db7f04ee56de;Parent=a4f932ba78c8de99;Sampled=1\n    [X-Adsk-Signature] => sha256=4217ca4f77c332dcf4596030e7d0d88e20406799a1262c12767c2f48da825131\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346516310-75526291637956\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:15:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346516310-75526291637956","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291637956","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:15:16.310Z"},"publishedAt":"2025-08-16T12:15:16.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:15:20
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:36]  Provided signature: sha256=038ba2fd064aca5ac848ecdcee6057092eca71d8c74ae5129aa6469ca1a2d2da
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:37]  Calculated signature: sha256=67067d46be542071368f870633ba28b7b96070a9fb4086791d081177ea9ff24e
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fc420b09b3c5d486\n    [X-B3-Traceid] => 68a076566408441248f921920d05a126\n    [B3] => 68a076566408441248f921920d05a126-fc420b09b3c5d486-1\n    [Traceparent] => 00-68a076566408441248f921920d05a126-fc420b09b3c5d486-01\n    [X-Amzn-Trace-Id] => Root=1-68a07656-6408441248f921920d05a126;Parent=fc420b09b3c5d486;Sampled=1\n    [X-Adsk-Signature] => sha256=038ba2fd064aca5ac848ecdcee6057092eca71d8c74ae5129aa6469ca1a2d2da\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346518354-75526291633555\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346518354-75526291633555","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291633555","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:15:18.354Z"},"publishedAt":"2025-08-16T12:15:18.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:15:20
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:36]  Provided signature: sha256=67067d46be542071368f870633ba28b7b96070a9fb4086791d081177ea9ff24e
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:37]  Calculated signature: sha256=67067d46be542071368f870633ba28b7b96070a9fb4086791d081177ea9ff24e
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0aae815a3cf953a9\n    [X-B3-Traceid] => 68a076566408441248f921920d05a126\n    [B3] => 68a076566408441248f921920d05a126-0aae815a3cf953a9-1\n    [Traceparent] => 00-68a076566408441248f921920d05a126-0aae815a3cf953a9-01\n    [X-Amzn-Trace-Id] => Root=1-68a07656-6408441248f921920d05a126;Parent=0aae815a3cf953a9;Sampled=1\n    [X-Adsk-Signature] => sha256=67067d46be542071368f870633ba28b7b96070a9fb4086791d081177ea9ff24e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346518354-75526291633555\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:15:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346518354-75526291633555","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526291633555","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:15:18.354Z"},"publishedAt":"2025-08-16T12:15:18.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:16:52
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:36]  Provided signature: sha256=ecb3e41367492adf31d2d5ecd75d7766bf5365200a8bdcc789061a4fb14c4bdc
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:37]  Calculated signature: sha256=af6032e977dbd9f669303a9a16e3c2683a9168c9d13ac19a76880fabf442001f
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6b9c87b5023074cb\n    [X-B3-Traceid] => 68a076b245eb0bf6635a19d169248447\n    [B3] => 68a076b245eb0bf6635a19d169248447-6b9c87b5023074cb-1\n    [Traceparent] => 00-68a076b245eb0bf6635a19d169248447-6b9c87b5023074cb-01\n    [X-Amzn-Trace-Id] => Root=1-68a076b2-45eb0bf6635a19d169248447;Parent=6b9c87b5023074cb;Sampled=1\n    [X-Adsk-Signature] => sha256=ecb3e41367492adf31d2d5ecd75d7766bf5365200a8bdcc789061a4fb14c4bdc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346610023-75526302267713\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346610023-75526302267713","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302267713","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:16:50.023Z"},"publishedAt":"2025-08-16T12:16:50.000Z","csn":"5103159758"}
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 12:16:52
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:36]  Provided signature: sha256=af6032e977dbd9f669303a9a16e3c2683a9168c9d13ac19a76880fabf442001f
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:37]  Calculated signature: sha256=af6032e977dbd9f669303a9a16e3c2683a9168c9d13ac19a76880fabf442001f
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 96054b7b410e0c1b\n    [X-B3-Traceid] => 68a076b245eb0bf6635a19d169248447\n    [B3] => 68a076b245eb0bf6635a19d169248447-96054b7b410e0c1b-1\n    [Traceparent] => 00-68a076b245eb0bf6635a19d169248447-96054b7b410e0c1b-01\n    [X-Amzn-Trace-Id] => Root=1-68a076b2-45eb0bf6635a19d169248447;Parent=96054b7b410e0c1b;Sampled=1\n    [X-Adsk-Signature] => sha256=af6032e977dbd9f669303a9a16e3c2683a9168c9d13ac19a76880fabf442001f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755346610023-75526302267713\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 12:16:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755346610023-75526302267713","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75526302267713","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-16T12:16:50.023Z"},"publishedAt":"2025-08-16T12:16:50.000Z","csn":"5103159758"}
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 23:01:41
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:36]  Provided signature: sha256=6e76a994aafbc79cd69b1ac63c18091723be5f27636648f7a1a8758cc92a894f
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:37]  Calculated signature: sha256=6e76a994aafbc79cd69b1ac63c18091723be5f27636648f7a1a8758cc92a894f
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 35cba0bc51630d47\n    [X-B3-Traceid] => 68a10dd37673ddbf025f78ef1bbc71c7\n    [B3] => 68a10dd37673ddbf025f78ef1bbc71c7-35cba0bc51630d47-1\n    [Traceparent] => 00-68a10dd37673ddbf025f78ef1bbc71c7-35cba0bc51630d47-01\n    [X-Amzn-Trace-Id] => Root=1-68a10dd3-7673ddbf025f78ef1bbc71c7;Parent=35cba0bc51630d47;Sampled=1\n    [X-Adsk-Signature] => sha256=6e76a994aafbc79cd69b1ac63c18091723be5f27636648f7a1a8758cc92a894f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755385299451-574-12577755\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755385299451-574-12577755","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-12577755","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-16T23:01:39.451Z"},"publishedAt":"2025-08-16T23:01:39.000Z","csn":"5103159758"}
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 23:01:41
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:36]  Provided signature: sha256=5c7b1d40bf066639a8c20c5aef3e03844eca275b33b88b31994ca1b27da97c96
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:37]  Calculated signature: sha256=6e76a994aafbc79cd69b1ac63c18091723be5f27636648f7a1a8758cc92a894f
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 76436772742faa4a\n    [X-B3-Traceid] => 68a10dd37673ddbf025f78ef1bbc71c7\n    [B3] => 68a10dd37673ddbf025f78ef1bbc71c7-76436772742faa4a-1\n    [Traceparent] => 00-68a10dd37673ddbf025f78ef1bbc71c7-76436772742faa4a-01\n    [X-Amzn-Trace-Id] => Root=1-68a10dd3-7673ddbf025f78ef1bbc71c7;Parent=76436772742faa4a;Sampled=1\n    [X-Adsk-Signature] => sha256=5c7b1d40bf066639a8c20c5aef3e03844eca275b33b88b31994ca1b27da97c96\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755385299451-574-12577755\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 23:01:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755385299451-574-12577755","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-12577755","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-16T23:01:39.451Z"},"publishedAt":"2025-08-16T23:01:39.000Z","csn":"5103159758"}
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 23:04:17
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:36]  Provided signature: sha256=51f838b27810d60b0ec370108b31f9ebfdad56818a0b034fa372b02cc553ebac
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:37]  Calculated signature: sha256=934aad36a377c087f4054b12e66a748a68281098080d95217536f9a40d9a0282
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0e323cc6cd727d0a\n    [X-B3-Traceid] => 68a10e6f56ff484a7aa8fe244d11fe93\n    [B3] => 68a10e6f56ff484a7aa8fe244d11fe93-0e323cc6cd727d0a-1\n    [Traceparent] => 00-68a10e6f56ff484a7aa8fe244d11fe93-0e323cc6cd727d0a-01\n    [X-Amzn-Trace-Id] => Root=1-68a10e6f-56ff484a7aa8fe244d11fe93;Parent=0e323cc6cd727d0a;Sampled=1\n    [X-Adsk-Signature] => sha256=51f838b27810d60b0ec370108b31f9ebfdad56818a0b034fa372b02cc553ebac\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755385455614-573-19751230\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755385455614-573-19751230","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"573-19751230","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-16T23:04:15.614Z"},"publishedAt":"2025-08-16T23:04:15.000Z","csn":"5103159758"}
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 23:04:17
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:36]  Provided signature: sha256=934aad36a377c087f4054b12e66a748a68281098080d95217536f9a40d9a0282
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:37]  Calculated signature: sha256=934aad36a377c087f4054b12e66a748a68281098080d95217536f9a40d9a0282
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d0007573528536fa\n    [X-B3-Traceid] => 68a10e6f56ff484a7aa8fe244d11fe93\n    [B3] => 68a10e6f56ff484a7aa8fe244d11fe93-d0007573528536fa-1\n    [Traceparent] => 00-68a10e6f56ff484a7aa8fe244d11fe93-d0007573528536fa-01\n    [X-Amzn-Trace-Id] => Root=1-68a10e6f-56ff484a7aa8fe244d11fe93;Parent=d0007573528536fa;Sampled=1\n    [X-Adsk-Signature] => sha256=934aad36a377c087f4054b12e66a748a68281098080d95217536f9a40d9a0282\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755385455614-573-19751230\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 23:04:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755385455614-573-19751230","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"573-19751230","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-16T23:04:15.614Z"},"publishedAt":"2025-08-16T23:04:15.000Z","csn":"5103159758"}
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 23:04:25
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:36]  Provided signature: sha256=bc1c6c5b1ac871fab44d59ececc0006da0bc522bbceca06e2397b44f4d45bdf9
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:37]  Calculated signature: sha256=bad143cbe9a84a966f60256cb2a729fbf54493ea8645cdf1434a4fd9208aa7db
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8a9865a60e37f1d0\n    [X-B3-Traceid] => 68a10e762f9f856463530bbe096c4e88\n    [B3] => 68a10e762f9f856463530bbe096c4e88-8a9865a60e37f1d0-1\n    [Traceparent] => 00-68a10e762f9f856463530bbe096c4e88-8a9865a60e37f1d0-01\n    [X-Amzn-Trace-Id] => Root=1-68a10e76-2f9f856463530bbe096c4e88;Parent=8a9865a60e37f1d0;Sampled=1\n    [X-Adsk-Signature] => sha256=bc1c6c5b1ac871fab44d59ececc0006da0bc522bbceca06e2397b44f4d45bdf9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755385462840-566-87318554\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755385462840-566-87318554","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"566-87318554","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-16T23:04:22.840Z"},"publishedAt":"2025-08-16T23:04:22.000Z","csn":"5103159758"}
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-16 23:04:25
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:36]  Provided signature: sha256=bad143cbe9a84a966f60256cb2a729fbf54493ea8645cdf1434a4fd9208aa7db
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:37]  Calculated signature: sha256=bad143cbe9a84a966f60256cb2a729fbf54493ea8645cdf1434a4fd9208aa7db
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 16c56227f8ebf725\n    [X-B3-Traceid] => 68a10e762f9f856463530bbe096c4e88\n    [B3] => 68a10e762f9f856463530bbe096c4e88-16c56227f8ebf725-1\n    [Traceparent] => 00-68a10e762f9f856463530bbe096c4e88-16c56227f8ebf725-01\n    [X-Amzn-Trace-Id] => Root=1-68a10e76-2f9f856463530bbe096c4e88;Parent=16c56227f8ebf725;Sampled=1\n    [X-Adsk-Signature] => sha256=bad143cbe9a84a966f60256cb2a729fbf54493ea8645cdf1434a4fd9208aa7db\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755385462840-566-87318554\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-16 23:04:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755385462840-566-87318554","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"566-87318554","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-16T23:04:22.840Z"},"publishedAt":"2025-08-16T23:04:22.000Z","csn":"5103159758"}
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 00:01:03
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:36]  Provided signature: sha256=74b1ccb399609ea73d184c35ee37c55345820f4b9b296a864f820f24c3a00f94
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:37]  Calculated signature: sha256=74b1ccb399609ea73d184c35ee37c55345820f4b9b296a864f820f24c3a00f94
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 88db05631f433cd1\n    [X-B3-Traceid] => 68a11bbc77fbf71c2b7c8ac634abe7fd\n    [B3] => 68a11bbc77fbf71c2b7c8ac634abe7fd-88db05631f433cd1-1\n    [Traceparent] => 00-68a11bbc77fbf71c2b7c8ac634abe7fd-88db05631f433cd1-01\n    [X-Amzn-Trace-Id] => Root=1-68a11bbc-77fbf71c2b7c8ac634abe7fd;Parent=88db05631f433cd1;Sampled=1\n    [X-Adsk-Signature] => sha256=74b1ccb399609ea73d184c35ee37c55345820f4b9b296a864f820f24c3a00f94\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9168bbed-670b-4644-b86f-a7eecce21d8f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"9168bbed-670b-4644-b86f-a7eecce21d8f","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-17T00:01:00Z"},"publishedAt":"2025-08-17T00:01:00.000Z","country":"GB"}
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 00:01:03
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:36]  Provided signature: sha256=9a22e074241ea163337208a4b55d28c33dab83191f2a706fac82c1ed6525308b
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:37]  Calculated signature: sha256=74b1ccb399609ea73d184c35ee37c55345820f4b9b296a864f820f24c3a00f94
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0b0e020bb675f39d\n    [X-B3-Traceid] => 68a11bbc77fbf71c2b7c8ac634abe7fd\n    [B3] => 68a11bbc77fbf71c2b7c8ac634abe7fd-0b0e020bb675f39d-1\n    [Traceparent] => 00-68a11bbc77fbf71c2b7c8ac634abe7fd-0b0e020bb675f39d-01\n    [X-Amzn-Trace-Id] => Root=1-68a11bbc-77fbf71c2b7c8ac634abe7fd;Parent=0b0e020bb675f39d;Sampled=1\n    [X-Adsk-Signature] => sha256=9a22e074241ea163337208a4b55d28c33dab83191f2a706fac82c1ed6525308b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9168bbed-670b-4644-b86f-a7eecce21d8f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-17 00:01:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"9168bbed-670b-4644-b86f-a7eecce21d8f","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-17T00:01:00Z"},"publishedAt":"2025-08-17T00:01:00.000Z","country":"GB"}
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 06:07:02
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:36]  Provided signature: sha256=e1ada675c50701901235224aba68f3b5260e5f88b2838b4b56271fb33f7af38e
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:37]  Calculated signature: sha256=e1ada675c50701901235224aba68f3b5260e5f88b2838b4b56271fb33f7af38e
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 04f15ab2f7f8aae7\n    [X-B3-Traceid] => 68a1718439d0328e3ed56dc101ae13a6\n    [B3] => 68a1718439d0328e3ed56dc101ae13a6-04f15ab2f7f8aae7-1\n    [Traceparent] => 00-68a1718439d0328e3ed56dc101ae13a6-04f15ab2f7f8aae7-01\n    [X-Amzn-Trace-Id] => Root=1-68a17184-39d0328e3ed56dc101ae13a6;Parent=04f15ab2f7f8aae7;Sampled=1\n    [X-Adsk-Signature] => sha256=e1ada675c50701901235224aba68f3b5260e5f88b2838b4b56271fb33f7af38e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => feb84a28-6a3f-4a1e-a8c5-c92235baff87\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"feb84a28-6a3f-4a1e-a8c5-c92235baff87","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59680488697967","status":"Expired","quantity":5,"message":"subscription status,quantity changed.","modifiedAt":"2025-08-17T05:36:58.000+0000"},"publishedAt":"2025-08-17T06:07:00.000Z","csn":"5103159758"}
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 06:07:02
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:36]  Provided signature: sha256=58a79d88c7f0fa423258962221425946a57b1aa2092ab51db5a7d788aa7625e0
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:37]  Calculated signature: sha256=e1ada675c50701901235224aba68f3b5260e5f88b2838b4b56271fb33f7af38e
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ed39e885abdfe909\n    [X-B3-Traceid] => 68a1718439d0328e3ed56dc101ae13a6\n    [B3] => 68a1718439d0328e3ed56dc101ae13a6-ed39e885abdfe909-1\n    [Traceparent] => 00-68a1718439d0328e3ed56dc101ae13a6-ed39e885abdfe909-01\n    [X-Amzn-Trace-Id] => Root=1-68a17184-39d0328e3ed56dc101ae13a6;Parent=ed39e885abdfe909;Sampled=1\n    [X-Adsk-Signature] => sha256=58a79d88c7f0fa423258962221425946a57b1aa2092ab51db5a7d788aa7625e0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => feb84a28-6a3f-4a1e-a8c5-c92235baff87\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-17 06:07:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"feb84a28-6a3f-4a1e-a8c5-c92235baff87","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59680488697967","status":"Expired","quantity":5,"message":"subscription status,quantity changed.","modifiedAt":"2025-08-17T05:36:58.000+0000"},"publishedAt":"2025-08-17T06:07:00.000Z","csn":"5103159758"}
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 16:38:14
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:36]  Provided signature: sha256=a2da53fb1bb6485e9c76906dd7879c5310747b0ea0f3bb9d0b5a235eed175637
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:37]  Calculated signature: sha256=a2da53fb1bb6485e9c76906dd7879c5310747b0ea0f3bb9d0b5a235eed175637
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => be9678b69a17d3ec\n    [X-B3-Traceid] => 68a20573605cc4e2215033f777de65cd\n    [B3] => 68a20573605cc4e2215033f777de65cd-be9678b69a17d3ec-1\n    [Traceparent] => 00-68a20573605cc4e2215033f777de65cd-be9678b69a17d3ec-01\n    [X-Amzn-Trace-Id] => Root=1-68a20573-605cc4e2215033f777de65cd;Parent=be9678b69a17d3ec;Sampled=1\n    [X-Adsk-Signature] => sha256=a2da53fb1bb6485e9c76906dd7879c5310747b0ea0f3bb9d0b5a235eed175637\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5b2562a3-3cc9-4637-8d01-b431fb6530c0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"5b2562a3-3cc9-4637-8d01-b431fb6530c0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73324048953791","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-17T16:23:08.000+0000"},"publishedAt":"2025-08-17T16:38:12.000Z","csn":"5103159758"}
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 16:38:14
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:36]  Provided signature: sha256=8cf6bbdc009add7e09db60dfdc1123d3fa9a9c93d43d6094e823ce5facf8a767
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:37]  Calculated signature: sha256=a2da53fb1bb6485e9c76906dd7879c5310747b0ea0f3bb9d0b5a235eed175637
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5caadbd30adc4dd9\n    [X-B3-Traceid] => 68a20573605cc4e2215033f777de65cd\n    [B3] => 68a20573605cc4e2215033f777de65cd-5caadbd30adc4dd9-1\n    [Traceparent] => 00-68a20573605cc4e2215033f777de65cd-5caadbd30adc4dd9-01\n    [X-Amzn-Trace-Id] => Root=1-68a20573-605cc4e2215033f777de65cd;Parent=5caadbd30adc4dd9;Sampled=1\n    [X-Adsk-Signature] => sha256=8cf6bbdc009add7e09db60dfdc1123d3fa9a9c93d43d6094e823ce5facf8a767\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5b2562a3-3cc9-4637-8d01-b431fb6530c0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-17 16:38:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"5b2562a3-3cc9-4637-8d01-b431fb6530c0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73324048953791","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-17T16:23:08.000+0000"},"publishedAt":"2025-08-17T16:38:12.000Z","csn":"5103159758"}
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 23:00:19
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 23:00:19
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:36]  Provided signature: sha256=b89aefcea045361242f56f2597803da0e0252256190dcd661a318fd2edbcf139
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:37]  Calculated signature: sha256=b89aefcea045361242f56f2597803da0e0252256190dcd661a318fd2edbcf139
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => af625a8ffd86da3f\n    [X-B3-Traceid] => 68a25efec0d662153403fec225d0f30f\n    [B3] => 68a25efec0d662153403fec225d0f30f-af625a8ffd86da3f-1\n    [Traceparent] => 00-68a25efec0d662153403fec225d0f30f-af625a8ffd86da3f-01\n    [X-Amzn-Trace-Id] => Root=1-68a25efe-c0d662153403fec225d0f30f;Parent=af625a8ffd86da3f;Sampled=1\n    [X-Adsk-Signature] => sha256=b89aefcea045361242f56f2597803da0e0252256190dcd661a318fd2edbcf139\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 95bf2e0b-a900-42da-9dc6-757ea3e257a2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"95bf2e0b-a900-42da-9dc6-757ea3e257a2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-942834","transactionId":"ad162168-1492-5f57-ac3f-419c4a175397","quoteStatus":"Expired","message":"Quote# Q-942834 status changed to Expired.","modifiedAt":"2025-08-17T23:00:13.939Z"},"publishedAt":"2025-08-17T23:00:14.000Z","csn":"5103159758"}
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:36]  Provided signature: sha256=6e8efab57323b2aa417f78f1a9a0aa320e6858ef130e1fe9b0f8305f7ef16bcf
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:37]  Calculated signature: sha256=b89aefcea045361242f56f2597803da0e0252256190dcd661a318fd2edbcf139
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f9c44527d2cbf02d\n    [X-B3-Traceid] => 68a25efec0d662153403fec225d0f30f\n    [B3] => 68a25efec0d662153403fec225d0f30f-f9c44527d2cbf02d-1\n    [Traceparent] => 00-68a25efec0d662153403fec225d0f30f-f9c44527d2cbf02d-01\n    [X-Amzn-Trace-Id] => Root=1-68a25efe-c0d662153403fec225d0f30f;Parent=f9c44527d2cbf02d;Sampled=1\n    [X-Adsk-Signature] => sha256=6e8efab57323b2aa417f78f1a9a0aa320e6858ef130e1fe9b0f8305f7ef16bcf\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 95bf2e0b-a900-42da-9dc6-757ea3e257a2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-17 23:00:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"95bf2e0b-a900-42da-9dc6-757ea3e257a2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-942834","transactionId":"ad162168-1492-5f57-ac3f-419c4a175397","quoteStatus":"Expired","message":"Quote# Q-942834 status changed to Expired.","modifiedAt":"2025-08-17T23:00:13.939Z"},"publishedAt":"2025-08-17T23:00:14.000Z","csn":"5103159758"}
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 23:00:35
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:36]  Provided signature: sha256=5f3d98690b22c12d924a8c8b4a0d81f3b79bb53e4a0429c6f603c678067b31b8
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:37]  Calculated signature: sha256=fb617d9ea4f1221357351748d45a7eca6486333c1db7a07af867ec4c462105be
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bb326c40148283e7\n    [X-B3-Traceid] => 68a25f10e540e3aaa8c86d40309bc0fc\n    [B3] => 68a25f10e540e3aaa8c86d40309bc0fc-bb326c40148283e7-1\n    [Traceparent] => 00-68a25f10e540e3aaa8c86d40309bc0fc-bb326c40148283e7-01\n    [X-Amzn-Trace-Id] => Root=1-68a25f10-e540e3aaa8c86d40309bc0fc;Parent=bb326c40148283e7;Sampled=1\n    [X-Adsk-Signature] => sha256=5f3d98690b22c12d924a8c8b4a0d81f3b79bb53e4a0429c6f603c678067b31b8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 440091ad-b922-4993-8d2f-1d55594c1501\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"440091ad-b922-4993-8d2f-1d55594c1501","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-942066","transactionId":"696e7c06-d4a7-5d7c-987b-1cf2cf3583ff","quoteStatus":"Expired","message":"Quote# Q-942066 status changed to Expired.","modifiedAt":"2025-08-17T23:00:28.380Z"},"publishedAt":"2025-08-17T23:00:33.000Z","csn":"5103159758"}
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 23:00:35
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:36]  Provided signature: sha256=fb617d9ea4f1221357351748d45a7eca6486333c1db7a07af867ec4c462105be
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:37]  Calculated signature: sha256=fb617d9ea4f1221357351748d45a7eca6486333c1db7a07af867ec4c462105be
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 45b13b8b2a8086dc\n    [X-B3-Traceid] => 68a25f10e540e3aaa8c86d40309bc0fc\n    [B3] => 68a25f10e540e3aaa8c86d40309bc0fc-45b13b8b2a8086dc-1\n    [Traceparent] => 00-68a25f10e540e3aaa8c86d40309bc0fc-45b13b8b2a8086dc-01\n    [X-Amzn-Trace-Id] => Root=1-68a25f10-e540e3aaa8c86d40309bc0fc;Parent=45b13b8b2a8086dc;Sampled=1\n    [X-Adsk-Signature] => sha256=fb617d9ea4f1221357351748d45a7eca6486333c1db7a07af867ec4c462105be\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 440091ad-b922-4993-8d2f-1d55594c1501\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-17 23:00:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"440091ad-b922-4993-8d2f-1d55594c1501","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-942066","transactionId":"696e7c06-d4a7-5d7c-987b-1cf2cf3583ff","quoteStatus":"Expired","message":"Quote# Q-942066 status changed to Expired.","modifiedAt":"2025-08-17T23:00:28.380Z"},"publishedAt":"2025-08-17T23:00:33.000Z","csn":"5103159758"}
[webhook] [2025-08-17 23:02:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 23:02:41
[webhook] [2025-08-17 23:02:41] [adwsapi_v2.php:36]  Provided signature: sha256=af099413856482fabda5ffb89fdba71df87101c3849127480de700e5e4fefbaf
[webhook] [2025-08-17 23:02:41] [adwsapi_v2.php:37]  Calculated signature: sha256=6ab7858942f878001f59c015bb39c2275cdac251489624c08fc830522cd86852
[webhook] [2025-08-17 23:02:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f96ac6ee01a5e6af\n    [X-B3-Traceid] => 68a25f8f2f4896e947eee5a463740895\n    [B3] => 68a25f8f2f4896e947eee5a463740895-f96ac6ee01a5e6af-1\n    [Traceparent] => 00-68a25f8f2f4896e947eee5a463740895-f96ac6ee01a5e6af-01\n    [X-Amzn-Trace-Id] => Root=1-68a25f8f-2f4896e947eee5a463740895;Parent=f96ac6ee01a5e6af;Sampled=1\n    [X-Adsk-Signature] => sha256=af099413856482fabda5ffb89fdba71df87101c3849127480de700e5e4fefbaf\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755471759798-574-95439412\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-17 23:02:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755471759798-574-95439412","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-95439412","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-17T23:02:39.798Z"},"publishedAt":"2025-08-17T23:02:39.000Z","csn":"5103159758"}
[webhook] [2025-08-17 23:02:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-17 23:02:42
[webhook] [2025-08-17 23:02:42] [adwsapi_v2.php:36]  Provided signature: sha256=6ab7858942f878001f59c015bb39c2275cdac251489624c08fc830522cd86852
[webhook] [2025-08-17 23:02:42] [adwsapi_v2.php:37]  Calculated signature: sha256=6ab7858942f878001f59c015bb39c2275cdac251489624c08fc830522cd86852
[webhook] [2025-08-17 23:02:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0ced38d6efdb4b3d\n    [X-B3-Traceid] => 68a25f8f2f4896e947eee5a463740895\n    [B3] => 68a25f8f2f4896e947eee5a463740895-0ced38d6efdb4b3d-1\n    [Traceparent] => 00-68a25f8f2f4896e947eee5a463740895-0ced38d6efdb4b3d-01\n    [X-Amzn-Trace-Id] => Root=1-68a25f8f-2f4896e947eee5a463740895;Parent=0ced38d6efdb4b3d;Sampled=1\n    [X-Adsk-Signature] => sha256=6ab7858942f878001f59c015bb39c2275cdac251489624c08fc830522cd86852\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755471759798-574-95439412\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-17 23:02:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755471759798-574-95439412","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-95439412","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-17T23:02:39.798Z"},"publishedAt":"2025-08-17T23:02:39.000Z","csn":"5103159758"}
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 00:01:02
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:36]  Provided signature: sha256=f53bf4fdb11d273f38ef213f4b7669a7761b25d82995907fda0c9c3d932dd7bb
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:37]  Calculated signature: sha256=8c1ca09c63c83d1b3bbd4275ffc874247e38675f7755cfe225045e36389cee0d
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a243af52d952bfa2\n    [X-B3-Traceid] => 68a26d3a474c9c6c1560a494599d1d5f\n    [B3] => 68a26d3a474c9c6c1560a494599d1d5f-a243af52d952bfa2-1\n    [Traceparent] => 00-68a26d3a474c9c6c1560a494599d1d5f-a243af52d952bfa2-01\n    [X-Amzn-Trace-Id] => Root=1-68a26d3a-474c9c6c1560a494599d1d5f;Parent=a243af52d952bfa2;Sampled=1\n    [X-Adsk-Signature] => sha256=f53bf4fdb11d273f38ef213f4b7669a7761b25d82995907fda0c9c3d932dd7bb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6fc53717-e104-4cfb-9bdb-aea804d7ce6f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"6fc53717-e104-4cfb-9bdb-aea804d7ce6f","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-18T00:00:59Z"},"publishedAt":"2025-08-18T00:00:59.000Z","country":"GB"}
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 00:01:02
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:36]  Provided signature: sha256=8c1ca09c63c83d1b3bbd4275ffc874247e38675f7755cfe225045e36389cee0d
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:37]  Calculated signature: sha256=8c1ca09c63c83d1b3bbd4275ffc874247e38675f7755cfe225045e36389cee0d
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f179dd54cd6b131c\n    [X-B3-Traceid] => 68a26d3a474c9c6c1560a494599d1d5f\n    [B3] => 68a26d3a474c9c6c1560a494599d1d5f-f179dd54cd6b131c-1\n    [Traceparent] => 00-68a26d3a474c9c6c1560a494599d1d5f-f179dd54cd6b131c-01\n    [X-Amzn-Trace-Id] => Root=1-68a26d3a-474c9c6c1560a494599d1d5f;Parent=f179dd54cd6b131c;Sampled=1\n    [X-Adsk-Signature] => sha256=8c1ca09c63c83d1b3bbd4275ffc874247e38675f7755cfe225045e36389cee0d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6fc53717-e104-4cfb-9bdb-aea804d7ce6f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 00:01:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"6fc53717-e104-4cfb-9bdb-aea804d7ce6f","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-18T00:00:59Z"},"publishedAt":"2025-08-18T00:00:59.000Z","country":"GB"}
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 00:10:59
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:36]  Provided signature: sha256=74ab578e1720dd8d7f3c81670f177b4a038734d52f1b35cdc0840fa8787991ee
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:37]  Calculated signature: sha256=74ab578e1720dd8d7f3c81670f177b4a038734d52f1b35cdc0840fa8787991ee
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 286d52e285abb06a\n    [X-B3-Traceid] => 68a26f8d18aeab9966bfb2e07c8444ef\n    [B3] => 68a26f8d18aeab9966bfb2e07c8444ef-286d52e285abb06a-1\n    [Traceparent] => 00-68a26f8d18aeab9966bfb2e07c8444ef-286d52e285abb06a-01\n    [X-Amzn-Trace-Id] => Root=1-68a26f8d-18aeab9966bfb2e07c8444ef;Parent=286d52e285abb06a;Sampled=1\n    [X-Adsk-Signature] => sha256=74ab578e1720dd8d7f3c81670f177b4a038734d52f1b35cdc0840fa8787991ee\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3b0bf735-1fda-4b96-813c-5c0199a76768\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"3b0bf735-1fda-4b96-813c-5c0199a76768","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56018211103443","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-17T23:40:47.000+0000"},"publishedAt":"2025-08-18T00:10:56.000Z","csn":"5103159758"}
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 00:10:59
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:36]  Provided signature: sha256=45f4c719ddbe667d1e35c46ff43f07125d8edc376b34e734eb0747423b65f949
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:37]  Calculated signature: sha256=74ab578e1720dd8d7f3c81670f177b4a038734d52f1b35cdc0840fa8787991ee
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 48ac56feab1c5456\n    [X-B3-Traceid] => 68a26f8d18aeab9966bfb2e07c8444ef\n    [B3] => 68a26f8d18aeab9966bfb2e07c8444ef-48ac56feab1c5456-1\n    [Traceparent] => 00-68a26f8d18aeab9966bfb2e07c8444ef-48ac56feab1c5456-01\n    [X-Amzn-Trace-Id] => Root=1-68a26f8d-18aeab9966bfb2e07c8444ef;Parent=48ac56feab1c5456;Sampled=1\n    [X-Adsk-Signature] => sha256=45f4c719ddbe667d1e35c46ff43f07125d8edc376b34e734eb0747423b65f949\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3b0bf735-1fda-4b96-813c-5c0199a76768\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 00:10:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"3b0bf735-1fda-4b96-813c-5c0199a76768","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56018211103443","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-08-17T23:40:47.000+0000"},"publishedAt":"2025-08-18T00:10:56.000Z","csn":"5103159758"}
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 08:24:11
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:36]  Provided signature: sha256=6c6194ed1346f3da446d146e13c5b23dd1e2ad4302e7d81a00991b384abad51e
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:37]  Calculated signature: sha256=caba911fc6ba8bb57e2f8961298b5b4c868dfd394958bada0717010235ecc204
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0ebfa7c54233af74\n    [X-B3-Traceid] => 68a2e329f1c9df9943cbbc94ec81ffbf\n    [B3] => 68a2e329f1c9df9943cbbc94ec81ffbf-0ebfa7c54233af74-1\n    [Traceparent] => 00-68a2e329f1c9df9943cbbc94ec81ffbf-0ebfa7c54233af74-01\n    [X-Amzn-Trace-Id] => Root=1-68a2e329-f1c9df9943cbbc94ec81ffbf;Parent=0ebfa7c54233af74;Sampled=1\n    [X-Adsk-Signature] => sha256=6c6194ed1346f3da446d146e13c5b23dd1e2ad4302e7d81a00991b384abad51e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 56b6cfff-3072-4dd2-b193-9ca33d891f2c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"56b6cfff-3072-4dd2-b193-9ca33d891f2c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-946524","transactionId":"1fae4ced-ccec-5644-b595-2c15ad85fb43","quoteStatus":"Order Submitted","message":"Quote# Q-946524 status changed to Order Submitted.","modifiedAt":"2025-08-18T08:24:08.687Z"},"publishedAt":"2025-08-18T08:24:09.000Z","csn":"5103159758"}
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 08:24:11
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:36]  Provided signature: sha256=caba911fc6ba8bb57e2f8961298b5b4c868dfd394958bada0717010235ecc204
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:37]  Calculated signature: sha256=caba911fc6ba8bb57e2f8961298b5b4c868dfd394958bada0717010235ecc204
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1e8dc287f679b41e\n    [X-B3-Traceid] => 68a2e329f1c9df9943cbbc94ec81ffbf\n    [B3] => 68a2e329f1c9df9943cbbc94ec81ffbf-1e8dc287f679b41e-1\n    [Traceparent] => 00-68a2e329f1c9df9943cbbc94ec81ffbf-1e8dc287f679b41e-01\n    [X-Amzn-Trace-Id] => Root=1-68a2e329-f1c9df9943cbbc94ec81ffbf;Parent=1e8dc287f679b41e;Sampled=1\n    [X-Adsk-Signature] => sha256=caba911fc6ba8bb57e2f8961298b5b4c868dfd394958bada0717010235ecc204\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 56b6cfff-3072-4dd2-b193-9ca33d891f2c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 08:24:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"56b6cfff-3072-4dd2-b193-9ca33d891f2c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-946524","transactionId":"1fae4ced-ccec-5644-b595-2c15ad85fb43","quoteStatus":"Order Submitted","message":"Quote# Q-946524 status changed to Order Submitted.","modifiedAt":"2025-08-18T08:24:08.687Z"},"publishedAt":"2025-08-18T08:24:09.000Z","csn":"5103159758"}
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 08:24:12
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:36]  Provided signature: sha256=4b6674c305d69a9d8f0b2b3c744897f8ad567d528373a994da4046f4aa1d7eb1
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:37]  Calculated signature: sha256=e80a6228dcf07181cdd66c386f795c435187626ffd4c34f126667054a71cc2da
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d8c491303363918d\n    [X-B3-Traceid] => 68a2e329e352d2438883da2addd679c2\n    [B3] => 68a2e329e352d2438883da2addd679c2-d8c491303363918d-1\n    [Traceparent] => 00-68a2e329e352d2438883da2addd679c2-d8c491303363918d-01\n    [X-Amzn-Trace-Id] => Root=1-68a2e329-e352d2438883da2addd679c2;Parent=d8c491303363918d;Sampled=1\n    [X-Adsk-Signature] => sha256=4b6674c305d69a9d8f0b2b3c744897f8ad567d528373a994da4046f4aa1d7eb1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c36bf642-fd91-43f0-845b-f4f7f185ca93\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"c36bf642-fd91-43f0-845b-f4f7f185ca93","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-946524","transactionId":"1fae4ced-ccec-5644-b595-2c15ad85fb43","quoteStatus":"Ordered","message":"Quote# Q-946524 status changed to Ordered.","modifiedAt":"2025-08-18T08:24:09.422Z"},"publishedAt":"2025-08-18T08:24:10.000Z","csn":"5103159758"}
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 08:24:12
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:36]  Provided signature: sha256=e80a6228dcf07181cdd66c386f795c435187626ffd4c34f126667054a71cc2da
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:37]  Calculated signature: sha256=e80a6228dcf07181cdd66c386f795c435187626ffd4c34f126667054a71cc2da
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6500363744323d98\n    [X-B3-Traceid] => 68a2e329e352d2438883da2addd679c2\n    [B3] => 68a2e329e352d2438883da2addd679c2-6500363744323d98-1\n    [Traceparent] => 00-68a2e329e352d2438883da2addd679c2-6500363744323d98-01\n    [X-Amzn-Trace-Id] => Root=1-68a2e329-e352d2438883da2addd679c2;Parent=6500363744323d98;Sampled=1\n    [X-Adsk-Signature] => sha256=e80a6228dcf07181cdd66c386f795c435187626ffd4c34f126667054a71cc2da\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c36bf642-fd91-43f0-845b-f4f7f185ca93\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 08:24:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"c36bf642-fd91-43f0-845b-f4f7f185ca93","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-946524","transactionId":"1fae4ced-ccec-5644-b595-2c15ad85fb43","quoteStatus":"Ordered","message":"Quote# Q-946524 status changed to Ordered.","modifiedAt":"2025-08-18T08:24:09.422Z"},"publishedAt":"2025-08-18T08:24:10.000Z","csn":"5103159758"}
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 08:39:19
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:36]  Provided signature: sha256=3e4a36a27577a760945718f212b20644f279252a06ee84219b509f914556bd5c
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:37]  Calculated signature: sha256=d874c25fd4b264e214893d11889b17efe0d59bf7fe83642fdcf449eddb1b0752
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7b3074a2a65970b2\n    [X-B3-Traceid] => 68a2e6b53f294eb92f758de55c3db9c1\n    [B3] => 68a2e6b53f294eb92f758de55c3db9c1-7b3074a2a65970b2-1\n    [Traceparent] => 00-68a2e6b53f294eb92f758de55c3db9c1-7b3074a2a65970b2-01\n    [X-Amzn-Trace-Id] => Root=1-68a2e6b5-3f294eb92f758de55c3db9c1;Parent=7b3074a2a65970b2;Sampled=1\n    [X-Adsk-Signature] => sha256=3e4a36a27577a760945718f212b20644f279252a06ee84219b509f914556bd5c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 99b4f49b-7b1a-475f-9002-2d6c852caf70\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"99b4f49b-7b1a-475f-9002-2d6c852caf70","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","status":"Active","quantity":2,"endDate":"2026-08-19","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T08:24:14.000+0000"},"publishedAt":"2025-08-18T08:39:17.000Z","csn":"5103159758"}
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 08:39:19
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:36]  Provided signature: sha256=d874c25fd4b264e214893d11889b17efe0d59bf7fe83642fdcf449eddb1b0752
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:37]  Calculated signature: sha256=d874c25fd4b264e214893d11889b17efe0d59bf7fe83642fdcf449eddb1b0752
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 683429f411f05127\n    [X-B3-Traceid] => 68a2e6b53f294eb92f758de55c3db9c1\n    [B3] => 68a2e6b53f294eb92f758de55c3db9c1-683429f411f05127-1\n    [Traceparent] => 00-68a2e6b53f294eb92f758de55c3db9c1-683429f411f05127-01\n    [X-Amzn-Trace-Id] => Root=1-68a2e6b5-3f294eb92f758de55c3db9c1;Parent=683429f411f05127;Sampled=1\n    [X-Adsk-Signature] => sha256=d874c25fd4b264e214893d11889b17efe0d59bf7fe83642fdcf449eddb1b0752\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 99b4f49b-7b1a-475f-9002-2d6c852caf70\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 08:39:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"99b4f49b-7b1a-475f-9002-2d6c852caf70","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","status":"Active","quantity":2,"endDate":"2026-08-19","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T08:24:14.000+0000"},"publishedAt":"2025-08-18T08:39:17.000Z","csn":"5103159758"}
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 09:40:45
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:36]  Provided signature: sha256=64bbb78582bd8ae58f3e9a2b9ed63942f8bd096e3c624777af7bfc2d798cb9ea
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:37]  Calculated signature: sha256=64bbb78582bd8ae58f3e9a2b9ed63942f8bd096e3c624777af7bfc2d798cb9ea
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 272f5b8f11fd356a\n    [X-B3-Traceid] => 68a2f51a5062f29b4437b02532ae7924\n    [B3] => 68a2f51a5062f29b4437b02532ae7924-272f5b8f11fd356a-1\n    [Traceparent] => 00-68a2f51a5062f29b4437b02532ae7924-272f5b8f11fd356a-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f51a-5062f29b4437b02532ae7924;Parent=272f5b8f11fd356a;Sampled=1\n    [X-Adsk-Signature] => sha256=64bbb78582bd8ae58f3e9a2b9ed63942f8bd096e3c624777af7bfc2d798cb9ea\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dae2732d-6f08-4ad9-b5e0-fd72899b6d52\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"dae2732d-6f08-4ad9-b5e0-fd72899b6d52","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-18T09:10:38.000+0000"},"publishedAt":"2025-08-18T09:40:42.000Z","csn":"5103159758"}
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 09:40:45
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:36]  Provided signature: sha256=bf7be2dd744e4f313aaa3018c0ddb1423505dec961264b1b6eef8c8b6b0e26c0
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:37]  Calculated signature: sha256=64bbb78582bd8ae58f3e9a2b9ed63942f8bd096e3c624777af7bfc2d798cb9ea
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 47411370811c5a7b\n    [X-B3-Traceid] => 68a2f51a5062f29b4437b02532ae7924\n    [B3] => 68a2f51a5062f29b4437b02532ae7924-47411370811c5a7b-1\n    [Traceparent] => 00-68a2f51a5062f29b4437b02532ae7924-47411370811c5a7b-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f51a-5062f29b4437b02532ae7924;Parent=47411370811c5a7b;Sampled=1\n    [X-Adsk-Signature] => sha256=bf7be2dd744e4f313aaa3018c0ddb1423505dec961264b1b6eef8c8b6b0e26c0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dae2732d-6f08-4ad9-b5e0-fd72899b6d52\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 09:40:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"dae2732d-6f08-4ad9-b5e0-fd72899b6d52","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-18T09:10:38.000+0000"},"publishedAt":"2025-08-18T09:40:42.000Z","csn":"5103159758"}
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 09:49:31
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:36]  Provided signature: sha256=c46b6be80f42c8e24d41bb7802dde23d8ed348f30aec33a1c49e00ae832a166a
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:37]  Calculated signature: sha256=c46b6be80f42c8e24d41bb7802dde23d8ed348f30aec33a1c49e00ae832a166a
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b6a60bc678ca5ab7\n    [X-B3-Traceid] => 68a2f7288e2a29c7c82a150bd5fb3ce0\n    [B3] => 68a2f7288e2a29c7c82a150bd5fb3ce0-b6a60bc678ca5ab7-1\n    [Traceparent] => 00-68a2f7288e2a29c7c82a150bd5fb3ce0-b6a60bc678ca5ab7-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f728-8e2a29c7c82a150bd5fb3ce0;Parent=b6a60bc678ca5ab7;Sampled=1\n    [X-Adsk-Signature] => sha256=c46b6be80f42c8e24d41bb7802dde23d8ed348f30aec33a1c49e00ae832a166a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7ace77cc-b2d1-4253-9fc9-2ee90ed62b1e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"7ace77cc-b2d1-4253-9fc9-2ee90ed62b1e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995460","transactionId":"c0894c4c-dae9-5350-af28-2d5f6272ab73","quoteStatus":"Order Submitted","message":"Quote# Q-995460 status changed to Order Submitted.","modifiedAt":"2025-08-18T09:49:28.131Z"},"publishedAt":"2025-08-18T09:49:28.000Z","csn":"5103159758"}
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 09:49:31
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:36]  Provided signature: sha256=3d1f224e2f03638b01e2e007ec2910beb7f3bfdcc0a0100de30eca73464dca30
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:37]  Calculated signature: sha256=c46b6be80f42c8e24d41bb7802dde23d8ed348f30aec33a1c49e00ae832a166a
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 73f77ef39adbbce7\n    [X-B3-Traceid] => 68a2f7288e2a29c7c82a150bd5fb3ce0\n    [B3] => 68a2f7288e2a29c7c82a150bd5fb3ce0-73f77ef39adbbce7-1\n    [Traceparent] => 00-68a2f7288e2a29c7c82a150bd5fb3ce0-73f77ef39adbbce7-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f728-8e2a29c7c82a150bd5fb3ce0;Parent=73f77ef39adbbce7;Sampled=1\n    [X-Adsk-Signature] => sha256=3d1f224e2f03638b01e2e007ec2910beb7f3bfdcc0a0100de30eca73464dca30\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7ace77cc-b2d1-4253-9fc9-2ee90ed62b1e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 09:49:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"7ace77cc-b2d1-4253-9fc9-2ee90ed62b1e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995460","transactionId":"c0894c4c-dae9-5350-af28-2d5f6272ab73","quoteStatus":"Order Submitted","message":"Quote# Q-995460 status changed to Order Submitted.","modifiedAt":"2025-08-18T09:49:28.131Z"},"publishedAt":"2025-08-18T09:49:28.000Z","csn":"5103159758"}
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 09:49:32
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:36]  Provided signature: sha256=67855ad7b3a7aa1f303a2a4e94d97fb4780c98b92e5dd09954bc314ed8424e06
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:37]  Calculated signature: sha256=67855ad7b3a7aa1f303a2a4e94d97fb4780c98b92e5dd09954bc314ed8424e06
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4feb8ad6e3285542\n    [X-B3-Traceid] => 68a2f72a8507842c6afae3a1d6b3d014\n    [B3] => 68a2f72a8507842c6afae3a1d6b3d014-4feb8ad6e3285542-1\n    [Traceparent] => 00-68a2f72a8507842c6afae3a1d6b3d014-4feb8ad6e3285542-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f72a-8507842c6afae3a1d6b3d014;Parent=4feb8ad6e3285542;Sampled=1\n    [X-Adsk-Signature] => sha256=67855ad7b3a7aa1f303a2a4e94d97fb4780c98b92e5dd09954bc314ed8424e06\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 041ba36d-68cf-4e16-a6cd-7bbe04638144\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"041ba36d-68cf-4e16-a6cd-7bbe04638144","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995460","transactionId":"c0894c4c-dae9-5350-af28-2d5f6272ab73","quoteStatus":"Ordered","message":"Quote# Q-995460 status changed to Ordered.","modifiedAt":"2025-08-18T09:49:30.488Z"},"publishedAt":"2025-08-18T09:49:30.000Z","csn":"5103159758"}
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 09:49:32
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:36]  Provided signature: sha256=85d931da89363b9a4a59e05a78b02fdfdec0a0dc1dfe5ab556161a78a1dc883d
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:37]  Calculated signature: sha256=67855ad7b3a7aa1f303a2a4e94d97fb4780c98b92e5dd09954bc314ed8424e06
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a0f9f75f94bce22f\n    [X-B3-Traceid] => 68a2f72a8507842c6afae3a1d6b3d014\n    [B3] => 68a2f72a8507842c6afae3a1d6b3d014-a0f9f75f94bce22f-1\n    [Traceparent] => 00-68a2f72a8507842c6afae3a1d6b3d014-a0f9f75f94bce22f-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f72a-8507842c6afae3a1d6b3d014;Parent=a0f9f75f94bce22f;Sampled=1\n    [X-Adsk-Signature] => sha256=85d931da89363b9a4a59e05a78b02fdfdec0a0dc1dfe5ab556161a78a1dc883d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 041ba36d-68cf-4e16-a6cd-7bbe04638144\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 09:49:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"041ba36d-68cf-4e16-a6cd-7bbe04638144","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995460","transactionId":"c0894c4c-dae9-5350-af28-2d5f6272ab73","quoteStatus":"Ordered","message":"Quote# Q-995460 status changed to Ordered.","modifiedAt":"2025-08-18T09:49:30.488Z"},"publishedAt":"2025-08-18T09:49:30.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:00:14
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:36]  Provided signature: sha256=59463a3891ec97fb497cc315db7e6de2099f444fc7e8688a2164e53e00c0fac7
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:37]  Calculated signature: sha256=fd00906ebffc62077cbd4a9b974e5a86184605a732b9dac3fe0b5adc04bf675d
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f6a5a843ec137270\n    [X-B3-Traceid] => 68a2f9acc0a026b6bc4aaea12d6a15b3\n    [B3] => 68a2f9acc0a026b6bc4aaea12d6a15b3-f6a5a843ec137270-1\n    [Traceparent] => 00-68a2f9acc0a026b6bc4aaea12d6a15b3-f6a5a843ec137270-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f9ac-c0a026b6bc4aaea12d6a15b3;Parent=f6a5a843ec137270;Sampled=1\n    [X-Adsk-Signature] => sha256=59463a3891ec97fb497cc315db7e6de2099f444fc7e8688a2164e53e00c0fac7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 614d607a-b729-4152-a936-43f1f6ff3106\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"614d607a-b729-4152-a936-43f1f6ff3106","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-992935","transactionId":"78671c3a-10ff-5a56-90cc-7d35c365807a","quoteStatus":"Quoted","message":"Quote# Q-992935 status changed to Quoted.","modifiedAt":"2025-08-18T10:00:11.919Z"},"publishedAt":"2025-08-18T10:00:12.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:00:14
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:36]  Provided signature: sha256=fd00906ebffc62077cbd4a9b974e5a86184605a732b9dac3fe0b5adc04bf675d
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:37]  Calculated signature: sha256=fd00906ebffc62077cbd4a9b974e5a86184605a732b9dac3fe0b5adc04bf675d
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9879c6a271dbf61b\n    [X-B3-Traceid] => 68a2f9acc0a026b6bc4aaea12d6a15b3\n    [B3] => 68a2f9acc0a026b6bc4aaea12d6a15b3-9879c6a271dbf61b-1\n    [Traceparent] => 00-68a2f9acc0a026b6bc4aaea12d6a15b3-9879c6a271dbf61b-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f9ac-c0a026b6bc4aaea12d6a15b3;Parent=9879c6a271dbf61b;Sampled=1\n    [X-Adsk-Signature] => sha256=fd00906ebffc62077cbd4a9b974e5a86184605a732b9dac3fe0b5adc04bf675d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 614d607a-b729-4152-a936-43f1f6ff3106\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:00:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"614d607a-b729-4152-a936-43f1f6ff3106","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-992935","transactionId":"78671c3a-10ff-5a56-90cc-7d35c365807a","quoteStatus":"Quoted","message":"Quote# Q-992935 status changed to Quoted.","modifiedAt":"2025-08-18T10:00:11.919Z"},"publishedAt":"2025-08-18T10:00:12.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:01:22
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:36]  Provided signature: sha256=96430a148350fe1b6f7a8324b7e808cdbdc2bf766bf228ec276ccfbca3ef7854
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:37]  Calculated signature: sha256=96430a148350fe1b6f7a8324b7e808cdbdc2bf766bf228ec276ccfbca3ef7854
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d33c263821e96413\n    [X-B3-Traceid] => 68a2f9ef45cf66d81009314a815cd4e8\n    [B3] => 68a2f9ef45cf66d81009314a815cd4e8-d33c263821e96413-1\n    [Traceparent] => 00-68a2f9ef45cf66d81009314a815cd4e8-d33c263821e96413-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f9ef-45cf66d81009314a815cd4e8;Parent=d33c263821e96413;Sampled=1\n    [X-Adsk-Signature] => sha256=96430a148350fe1b6f7a8324b7e808cdbdc2bf766bf228ec276ccfbca3ef7854\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ab98f2d5-fd5c-4375-8134-b4459af1e85e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"ab98f2d5-fd5c-4375-8134-b4459af1e85e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995862","transactionId":"1d338cb2-a9f8-5a9c-a396-eda17798a6a2","quoteStatus":"Cancelled","message":"Quote# Q-995862 status changed to Cancelled.","modifiedAt":"2025-08-18T10:01:19.161Z"},"publishedAt":"2025-08-18T10:01:19.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:01:22
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:36]  Provided signature: sha256=5a531f62710f923026271761daaca980f6f4cd54f594f555a4eaa65306b0ad15
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:37]  Calculated signature: sha256=96430a148350fe1b6f7a8324b7e808cdbdc2bf766bf228ec276ccfbca3ef7854
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b53880518f36cc15\n    [X-B3-Traceid] => 68a2f9ef45cf66d81009314a815cd4e8\n    [B3] => 68a2f9ef45cf66d81009314a815cd4e8-b53880518f36cc15-1\n    [Traceparent] => 00-68a2f9ef45cf66d81009314a815cd4e8-b53880518f36cc15-01\n    [X-Amzn-Trace-Id] => Root=1-68a2f9ef-45cf66d81009314a815cd4e8;Parent=b53880518f36cc15;Sampled=1\n    [X-Adsk-Signature] => sha256=5a531f62710f923026271761daaca980f6f4cd54f594f555a4eaa65306b0ad15\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ab98f2d5-fd5c-4375-8134-b4459af1e85e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:01:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"ab98f2d5-fd5c-4375-8134-b4459af1e85e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995862","transactionId":"1d338cb2-a9f8-5a9c-a396-eda17798a6a2","quoteStatus":"Cancelled","message":"Quote# Q-995862 status changed to Cancelled.","modifiedAt":"2025-08-18T10:01:19.161Z"},"publishedAt":"2025-08-18T10:01:19.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:09:44
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:36]  Provided signature: sha256=76bd97bcc480556b68159721ef06717cae3b6ef2d53fd3277b61e0d30d3f80b1
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:37]  Calculated signature: sha256=76bd97bcc480556b68159721ef06717cae3b6ef2d53fd3277b61e0d30d3f80b1
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2254467651e20d87\n    [X-B3-Traceid] => 68a2fbe6355a3f7f09eaa0287c7d8a2c\n    [B3] => 68a2fbe6355a3f7f09eaa0287c7d8a2c-2254467651e20d87-1\n    [Traceparent] => 00-68a2fbe6355a3f7f09eaa0287c7d8a2c-2254467651e20d87-01\n    [X-Amzn-Trace-Id] => Root=1-68a2fbe6-355a3f7f09eaa0287c7d8a2c;Parent=2254467651e20d87;Sampled=1\n    [X-Adsk-Signature] => sha256=76bd97bcc480556b68159721ef06717cae3b6ef2d53fd3277b61e0d30d3f80b1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bbbf68f9-176f-4876-97c0-e2bb6e370939\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"bbbf68f9-176f-4876-97c0-e2bb6e370939","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","status":"Active","quantity":1,"endDate":"2026-09-12","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T09:49:39.000+0000"},"publishedAt":"2025-08-18T10:09:42.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:09:44
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:36]  Provided signature: sha256=0fb7fe8f624c1c1474955b3e295223fef3720f745518a2528af0476882b796a4
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:37]  Calculated signature: sha256=76bd97bcc480556b68159721ef06717cae3b6ef2d53fd3277b61e0d30d3f80b1
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ec9fa20a72d1d00b\n    [X-B3-Traceid] => 68a2fbe6355a3f7f09eaa0287c7d8a2c\n    [B3] => 68a2fbe6355a3f7f09eaa0287c7d8a2c-ec9fa20a72d1d00b-1\n    [Traceparent] => 00-68a2fbe6355a3f7f09eaa0287c7d8a2c-ec9fa20a72d1d00b-01\n    [X-Amzn-Trace-Id] => Root=1-68a2fbe6-355a3f7f09eaa0287c7d8a2c;Parent=ec9fa20a72d1d00b;Sampled=1\n    [X-Adsk-Signature] => sha256=0fb7fe8f624c1c1474955b3e295223fef3720f745518a2528af0476882b796a4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bbbf68f9-176f-4876-97c0-e2bb6e370939\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:09:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"bbbf68f9-176f-4876-97c0-e2bb6e370939","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","status":"Active","quantity":1,"endDate":"2026-09-12","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T09:49:39.000+0000"},"publishedAt":"2025-08-18T10:09:42.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:41:42
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:36]  Provided signature: sha256=e6eb966654d813f5f3dd3650c4e8505ec4bb6b1ca701e05d339ee42856959796
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:37]  Calculated signature: sha256=f296f113490c1eff100b7715c0858a4a0c12dd288bd0ade63af545df8f7ee3b9
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cf48698e8fcd248f\n    [X-B3-Traceid] => 68a303643aa1779d4ce390ef429fd753\n    [B3] => 68a303643aa1779d4ce390ef429fd753-cf48698e8fcd248f-1\n    [Traceparent] => 00-68a303643aa1779d4ce390ef429fd753-cf48698e8fcd248f-01\n    [X-Amzn-Trace-Id] => Root=1-68a30364-3aa1779d4ce390ef429fd753;Parent=cf48698e8fcd248f;Sampled=1\n    [X-Adsk-Signature] => sha256=e6eb966654d813f5f3dd3650c4e8505ec4bb6b1ca701e05d339ee42856959796\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 92dd69fb-1e36-4253-83ea-fd348ee70c24\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"92dd69fb-1e36-4253-83ea-fd348ee70c24","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","status":"Active","quantity":1,"endDate":"2026-09-30","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T10:16:36.000+0000"},"publishedAt":"2025-08-18T10:41:40.000Z","csn":"5103159758"}
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 10:41:42
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:36]  Provided signature: sha256=f296f113490c1eff100b7715c0858a4a0c12dd288bd0ade63af545df8f7ee3b9
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:37]  Calculated signature: sha256=f296f113490c1eff100b7715c0858a4a0c12dd288bd0ade63af545df8f7ee3b9
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ec284ace01571056\n    [X-B3-Traceid] => 68a303643aa1779d4ce390ef429fd753\n    [B3] => 68a303643aa1779d4ce390ef429fd753-ec284ace01571056-1\n    [Traceparent] => 00-68a303643aa1779d4ce390ef429fd753-ec284ace01571056-01\n    [X-Amzn-Trace-Id] => Root=1-68a30364-3aa1779d4ce390ef429fd753;Parent=ec284ace01571056;Sampled=1\n    [X-Adsk-Signature] => sha256=f296f113490c1eff100b7715c0858a4a0c12dd288bd0ade63af545df8f7ee3b9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 92dd69fb-1e36-4253-83ea-fd348ee70c24\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 10:41:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"92dd69fb-1e36-4253-83ea-fd348ee70c24","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","status":"Active","quantity":1,"endDate":"2026-09-30","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T10:16:36.000+0000"},"publishedAt":"2025-08-18T10:41:40.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:03:18
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:36]  Provided signature: sha256=e2c6bc344bc6e1347847ba4c6fb28d5a80af0bc767154f8b00a3619799bb35fe
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:37]  Calculated signature: sha256=af49ef4b2c15a4a44913b63419e818412d124dff3b68c981589eca7dc19ec1d4
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 826fde8794a70b94\n    [X-B3-Traceid] => 68a308730d91c28f311189684b1829a4\n    [B3] => 68a308730d91c28f311189684b1829a4-826fde8794a70b94-1\n    [Traceparent] => 00-68a308730d91c28f311189684b1829a4-826fde8794a70b94-01\n    [X-Amzn-Trace-Id] => Root=1-68a30873-0d91c28f311189684b1829a4;Parent=826fde8794a70b94;Sampled=1\n    [X-Adsk-Signature] => sha256=e2c6bc344bc6e1347847ba4c6fb28d5a80af0bc767154f8b00a3619799bb35fe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 89700b65-4a81-4d8a-90c7-da6a968c349a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"89700b65-4a81-4d8a-90c7-da6a968c349a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Order Submitted","message":"Quote# Q-1008618 status changed to Order Submitted.","modifiedAt":"2025-08-18T11:03:15.168Z"},"publishedAt":"2025-08-18T11:03:15.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:03:18
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:36]  Provided signature: sha256=af49ef4b2c15a4a44913b63419e818412d124dff3b68c981589eca7dc19ec1d4
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:37]  Calculated signature: sha256=af49ef4b2c15a4a44913b63419e818412d124dff3b68c981589eca7dc19ec1d4
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6c4c9045e3e18ad8\n    [X-B3-Traceid] => 68a308730d91c28f311189684b1829a4\n    [B3] => 68a308730d91c28f311189684b1829a4-6c4c9045e3e18ad8-1\n    [Traceparent] => 00-68a308730d91c28f311189684b1829a4-6c4c9045e3e18ad8-01\n    [X-Amzn-Trace-Id] => Root=1-68a30873-0d91c28f311189684b1829a4;Parent=6c4c9045e3e18ad8;Sampled=1\n    [X-Adsk-Signature] => sha256=af49ef4b2c15a4a44913b63419e818412d124dff3b68c981589eca7dc19ec1d4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 89700b65-4a81-4d8a-90c7-da6a968c349a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"89700b65-4a81-4d8a-90c7-da6a968c349a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Order Submitted","message":"Quote# Q-1008618 status changed to Order Submitted.","modifiedAt":"2025-08-18T11:03:15.168Z"},"publishedAt":"2025-08-18T11:03:15.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:03:18
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:36]  Provided signature: sha256=52996d26f6313b4a8e74637edfd1fa2d3349964e010e605b529d7d4f50f1f379
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:37]  Calculated signature: sha256=94c76ffb77f7af910b65f62845ac7e642c897ea0299467d54877422d048383bd
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 911426e3091fd1d9\n    [X-B3-Traceid] => 68a308749312cb19c3aa9f1e07c6af7c\n    [B3] => 68a308749312cb19c3aa9f1e07c6af7c-911426e3091fd1d9-1\n    [Traceparent] => 00-68a308749312cb19c3aa9f1e07c6af7c-911426e3091fd1d9-01\n    [X-Amzn-Trace-Id] => Root=1-68a30874-9312cb19c3aa9f1e07c6af7c;Parent=911426e3091fd1d9;Sampled=1\n    [X-Adsk-Signature] => sha256=52996d26f6313b4a8e74637edfd1fa2d3349964e010e605b529d7d4f50f1f379\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 246484ae-3971-45b4-a6c4-e275dbe9c70f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:03:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"246484ae-3971-45b4-a6c4-e275dbe9c70f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Ordered","message":"Quote# Q-1008618 status changed to Ordered.","modifiedAt":"2025-08-18T11:03:15.991Z"},"publishedAt":"2025-08-18T11:03:16.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:03:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:03:19
[webhook] [2025-08-18 11:03:19] [adwsapi_v2.php:36]  Provided signature: sha256=94c76ffb77f7af910b65f62845ac7e642c897ea0299467d54877422d048383bd
[webhook] [2025-08-18 11:03:19] [adwsapi_v2.php:37]  Calculated signature: sha256=94c76ffb77f7af910b65f62845ac7e642c897ea0299467d54877422d048383bd
[webhook] [2025-08-18 11:03:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8e0e80985cbcc7cb\n    [X-B3-Traceid] => 68a308749312cb19c3aa9f1e07c6af7c\n    [B3] => 68a308749312cb19c3aa9f1e07c6af7c-8e0e80985cbcc7cb-1\n    [Traceparent] => 00-68a308749312cb19c3aa9f1e07c6af7c-8e0e80985cbcc7cb-01\n    [X-Amzn-Trace-Id] => Root=1-68a30874-9312cb19c3aa9f1e07c6af7c;Parent=8e0e80985cbcc7cb;Sampled=1\n    [X-Adsk-Signature] => sha256=94c76ffb77f7af910b65f62845ac7e642c897ea0299467d54877422d048383bd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 246484ae-3971-45b4-a6c4-e275dbe9c70f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:03:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"246484ae-3971-45b4-a6c4-e275dbe9c70f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1008618","transactionId":"095f7cfb-ee74-5b2e-812a-1de319441bb2","quoteStatus":"Ordered","message":"Quote# Q-1008618 status changed to Ordered.","modifiedAt":"2025-08-18T11:03:15.991Z"},"publishedAt":"2025-08-18T11:03:16.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:09:53
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:36]  Provided signature: sha256=2fe426307a71bc86072020b13644e384cce9d0133d381c55bf3b37f77a9441a8
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:37]  Calculated signature: sha256=763077475d746d4520f97af0c3438b5314795c096388a028c835e935fde8e429
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5a1fe8271aeb4bc9\n    [X-B3-Traceid] => 68a309fe19a52a525b798b4c25e71db5\n    [B3] => 68a309fe19a52a525b798b4c25e71db5-5a1fe8271aeb4bc9-1\n    [Traceparent] => 00-68a309fe19a52a525b798b4c25e71db5-5a1fe8271aeb4bc9-01\n    [X-Amzn-Trace-Id] => Root=1-68a309fe-19a52a525b798b4c25e71db5;Parent=5a1fe8271aeb4bc9;Sampled=1\n    [X-Adsk-Signature] => sha256=2fe426307a71bc86072020b13644e384cce9d0133d381c55bf3b37f77a9441a8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b7c8d6e6-d342-4cf9-b8ec-5a75b4ac3f60\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"b7c8d6e6-d342-4cf9-b8ec-5a75b4ac3f60","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-18T10:39:48.000+0000"},"publishedAt":"2025-08-18T11:09:50.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:09:53
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:36]  Provided signature: sha256=763077475d746d4520f97af0c3438b5314795c096388a028c835e935fde8e429
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:37]  Calculated signature: sha256=763077475d746d4520f97af0c3438b5314795c096388a028c835e935fde8e429
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f86f44a9da811fab\n    [X-B3-Traceid] => 68a309fe19a52a525b798b4c25e71db5\n    [B3] => 68a309fe19a52a525b798b4c25e71db5-f86f44a9da811fab-1\n    [Traceparent] => 00-68a309fe19a52a525b798b4c25e71db5-f86f44a9da811fab-01\n    [X-Amzn-Trace-Id] => Root=1-68a309fe-19a52a525b798b4c25e71db5;Parent=f86f44a9da811fab;Sampled=1\n    [X-Adsk-Signature] => sha256=763077475d746d4520f97af0c3438b5314795c096388a028c835e935fde8e429\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b7c8d6e6-d342-4cf9-b8ec-5a75b4ac3f60\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:09:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"b7c8d6e6-d342-4cf9-b8ec-5a75b4ac3f60","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-18T10:39:48.000+0000"},"publishedAt":"2025-08-18T11:09:50.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:35:47
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:36]  Provided signature: sha256=dd7c785d87f89fc34f5e54f5a0c563842c14905380340b5c6a4fd9aeb24ab890
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:37]  Calculated signature: sha256=0f466ae727bbf891c9e552b874227717f3d1fc06516227c5417c286ed3e87dd9
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3e8bde7a35d15131\n    [X-B3-Traceid] => 68a310110ce6797c00a1ec070d89e631\n    [B3] => 68a310110ce6797c00a1ec070d89e631-3e8bde7a35d15131-1\n    [Traceparent] => 00-68a310110ce6797c00a1ec070d89e631-3e8bde7a35d15131-01\n    [X-Amzn-Trace-Id] => Root=1-68a31011-0ce6797c00a1ec070d89e631;Parent=3e8bde7a35d15131;Sampled=1\n    [X-Adsk-Signature] => sha256=dd7c785d87f89fc34f5e54f5a0c563842c14905380340b5c6a4fd9aeb24ab890\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f1923fc7-7838-4f87-8e12-4d9553e84e04\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"f1923fc7-7838-4f87-8e12-4d9553e84e04","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:10:35.000+0000"},"publishedAt":"2025-08-18T11:35:45.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:35:47
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:36]  Provided signature: sha256=0f466ae727bbf891c9e552b874227717f3d1fc06516227c5417c286ed3e87dd9
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:37]  Calculated signature: sha256=0f466ae727bbf891c9e552b874227717f3d1fc06516227c5417c286ed3e87dd9
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8ec8a882ac7235ac\n    [X-B3-Traceid] => 68a310110ce6797c00a1ec070d89e631\n    [B3] => 68a310110ce6797c00a1ec070d89e631-8ec8a882ac7235ac-1\n    [Traceparent] => 00-68a310110ce6797c00a1ec070d89e631-8ec8a882ac7235ac-01\n    [X-Amzn-Trace-Id] => Root=1-68a31011-0ce6797c00a1ec070d89e631;Parent=8ec8a882ac7235ac;Sampled=1\n    [X-Adsk-Signature] => sha256=0f466ae727bbf891c9e552b874227717f3d1fc06516227c5417c286ed3e87dd9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f1923fc7-7838-4f87-8e12-4d9553e84e04\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:35:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"f1923fc7-7838-4f87-8e12-4d9553e84e04","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:10:35.000+0000"},"publishedAt":"2025-08-18T11:35:45.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:36:15
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:36]  Provided signature: sha256=bd6ef7b7d396c8ec4547cd1bc45537c03f85fa3134aafba13f809b75427bc8fa
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:37]  Calculated signature: sha256=b2a03fca2a3c5d54f6de6e596a0324ab89d6c476bd3e96691f6fbd1f9ce276b5
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a18c10d097f36841\n    [X-B3-Traceid] => 68a3102d101b833333addc5e3005bb83\n    [B3] => 68a3102d101b833333addc5e3005bb83-a18c10d097f36841-1\n    [Traceparent] => 00-68a3102d101b833333addc5e3005bb83-a18c10d097f36841-01\n    [X-Amzn-Trace-Id] => Root=1-68a3102d-101b833333addc5e3005bb83;Parent=a18c10d097f36841;Sampled=1\n    [X-Adsk-Signature] => sha256=bd6ef7b7d396c8ec4547cd1bc45537c03f85fa3134aafba13f809b75427bc8fa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 631f62f8-2b3e-4f85-96d7-e5f745d722a1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"631f62f8-2b3e-4f85-96d7-e5f745d722a1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:11:03.000+0000"},"publishedAt":"2025-08-18T11:36:13.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:36:15
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:36]  Provided signature: sha256=b2a03fca2a3c5d54f6de6e596a0324ab89d6c476bd3e96691f6fbd1f9ce276b5
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:37]  Calculated signature: sha256=b2a03fca2a3c5d54f6de6e596a0324ab89d6c476bd3e96691f6fbd1f9ce276b5
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 32d30cd78846f791\n    [X-B3-Traceid] => 68a3102d101b833333addc5e3005bb83\n    [B3] => 68a3102d101b833333addc5e3005bb83-32d30cd78846f791-1\n    [Traceparent] => 00-68a3102d101b833333addc5e3005bb83-32d30cd78846f791-01\n    [X-Amzn-Trace-Id] => Root=1-68a3102d-101b833333addc5e3005bb83;Parent=32d30cd78846f791;Sampled=1\n    [X-Adsk-Signature] => sha256=b2a03fca2a3c5d54f6de6e596a0324ab89d6c476bd3e96691f6fbd1f9ce276b5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 631f62f8-2b3e-4f85-96d7-e5f745d722a1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:36:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"631f62f8-2b3e-4f85-96d7-e5f745d722a1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:11:03.000+0000"},"publishedAt":"2025-08-18T11:36:13.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:37:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:37:46
[webhook] [2025-08-18 11:37:46] [adwsapi_v2.php:36]  Provided signature: sha256=535f6cec91a8dc4fc1050e244a389140177d1c068bb85de0bcc6fd52ff81dc15
[webhook] [2025-08-18 11:37:46] [adwsapi_v2.php:37]  Calculated signature: sha256=535f6cec91a8dc4fc1050e244a389140177d1c068bb85de0bcc6fd52ff81dc15
[webhook] [2025-08-18 11:37:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f281ab3760a2a7a4\n    [X-B3-Traceid] => 68a310886f64158a5f3af3763a3f1444\n    [B3] => 68a310886f64158a5f3af3763a3f1444-f281ab3760a2a7a4-1\n    [Traceparent] => 00-68a310886f64158a5f3af3763a3f1444-f281ab3760a2a7a4-01\n    [X-Amzn-Trace-Id] => Root=1-68a31088-6f64158a5f3af3763a3f1444;Parent=f281ab3760a2a7a4;Sampled=1\n    [X-Adsk-Signature] => sha256=535f6cec91a8dc4fc1050e244a389140177d1c068bb85de0bcc6fd52ff81dc15\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bb1bd055-663b-4cf0-89b7-43659f2f55e1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:37:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"bb1bd055-663b-4cf0-89b7-43659f2f55e1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056564098","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:16:29.000+0000"},"publishedAt":"2025-08-18T11:37:45.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:37:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:37:47
[webhook] [2025-08-18 11:37:47] [adwsapi_v2.php:36]  Provided signature: sha256=04788f920ec5a1c363b9c492e7e3df79c1bf119d35e8b8dcf16a313873de74d3
[webhook] [2025-08-18 11:37:47] [adwsapi_v2.php:37]  Calculated signature: sha256=535f6cec91a8dc4fc1050e244a389140177d1c068bb85de0bcc6fd52ff81dc15
[webhook] [2025-08-18 11:37:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => de6c2316d755c401\n    [X-B3-Traceid] => 68a310886f64158a5f3af3763a3f1444\n    [B3] => 68a310886f64158a5f3af3763a3f1444-de6c2316d755c401-1\n    [Traceparent] => 00-68a310886f64158a5f3af3763a3f1444-de6c2316d755c401-01\n    [X-Amzn-Trace-Id] => Root=1-68a31088-6f64158a5f3af3763a3f1444;Parent=de6c2316d755c401;Sampled=1\n    [X-Adsk-Signature] => sha256=04788f920ec5a1c363b9c492e7e3df79c1bf119d35e8b8dcf16a313873de74d3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bb1bd055-663b-4cf0-89b7-43659f2f55e1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:37:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"bb1bd055-663b-4cf0-89b7-43659f2f55e1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056564098","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:16:29.000+0000"},"publishedAt":"2025-08-18T11:37:45.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:37:52
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:36]  Provided signature: sha256=8505201e2c651a1fad17e85efa49fe2141b3125609658360c851b160f6ce5d5f
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:37]  Calculated signature: sha256=5ac3c770321cee58b2c4d4d99e821275bdc5bec537b7a96578974b7d5ebcf474
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7fc2aa12a05d853f\n    [X-B3-Traceid] => 68a3108e301a52c716145e7415afb60d\n    [B3] => 68a3108e301a52c716145e7415afb60d-7fc2aa12a05d853f-1\n    [Traceparent] => 00-68a3108e301a52c716145e7415afb60d-7fc2aa12a05d853f-01\n    [X-Amzn-Trace-Id] => Root=1-68a3108e-301a52c716145e7415afb60d;Parent=7fc2aa12a05d853f;Sampled=1\n    [X-Adsk-Signature] => sha256=8505201e2c651a1fad17e85efa49fe2141b3125609658360c851b160f6ce5d5f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b9463980-2ccb-4840-a786-f708957ec7f5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"b9463980-2ccb-4840-a786-f708957ec7f5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056568699","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:16:29.000+0000"},"publishedAt":"2025-08-18T11:37:50.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:37:52
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:36]  Provided signature: sha256=5ac3c770321cee58b2c4d4d99e821275bdc5bec537b7a96578974b7d5ebcf474
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:37]  Calculated signature: sha256=5ac3c770321cee58b2c4d4d99e821275bdc5bec537b7a96578974b7d5ebcf474
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 428bf467f233778b\n    [X-B3-Traceid] => 68a3108e301a52c716145e7415afb60d\n    [B3] => 68a3108e301a52c716145e7415afb60d-428bf467f233778b-1\n    [Traceparent] => 00-68a3108e301a52c716145e7415afb60d-428bf467f233778b-01\n    [X-Amzn-Trace-Id] => Root=1-68a3108e-301a52c716145e7415afb60d;Parent=428bf467f233778b;Sampled=1\n    [X-Adsk-Signature] => sha256=5ac3c770321cee58b2c4d4d99e821275bdc5bec537b7a96578974b7d5ebcf474\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b9463980-2ccb-4840-a786-f708957ec7f5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:37:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"b9463980-2ccb-4840-a786-f708957ec7f5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056568699","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:16:29.000+0000"},"publishedAt":"2025-08-18T11:37:50.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:38:29
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:36]  Provided signature: sha256=bbfc8db7529d105509769bb8c38285e54a00bd74c7ceabfd3977800c47226775
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:37]  Calculated signature: sha256=80d894ae302a90ab537606f77429d7abdcfe05985617b0dc6e4c13dcec689b6d
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 37f64c9ca88a5943\n    [X-B3-Traceid] => 68a310b3521f421a3b563f9600f2312e\n    [B3] => 68a310b3521f421a3b563f9600f2312e-37f64c9ca88a5943-1\n    [Traceparent] => 00-68a310b3521f421a3b563f9600f2312e-37f64c9ca88a5943-01\n    [X-Amzn-Trace-Id] => Root=1-68a310b3-521f421a3b563f9600f2312e;Parent=37f64c9ca88a5943;Sampled=1\n    [X-Adsk-Signature] => sha256=bbfc8db7529d105509769bb8c38285e54a00bd74c7ceabfd3977800c47226775\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3ee3a6db-c6b9-4534-bb76-d8a9e2b436f5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"3ee3a6db-c6b9-4534-bb76-d8a9e2b436f5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551499191069","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:17:06.000+0000"},"publishedAt":"2025-08-18T11:38:27.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:38:29
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:36]  Provided signature: sha256=80d894ae302a90ab537606f77429d7abdcfe05985617b0dc6e4c13dcec689b6d
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:37]  Calculated signature: sha256=80d894ae302a90ab537606f77429d7abdcfe05985617b0dc6e4c13dcec689b6d
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5a6db70ce87bbfe2\n    [X-B3-Traceid] => 68a310b3521f421a3b563f9600f2312e\n    [B3] => 68a310b3521f421a3b563f9600f2312e-5a6db70ce87bbfe2-1\n    [Traceparent] => 00-68a310b3521f421a3b563f9600f2312e-5a6db70ce87bbfe2-01\n    [X-Amzn-Trace-Id] => Root=1-68a310b3-521f421a3b563f9600f2312e;Parent=5a6db70ce87bbfe2;Sampled=1\n    [X-Adsk-Signature] => sha256=80d894ae302a90ab537606f77429d7abdcfe05985617b0dc6e4c13dcec689b6d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3ee3a6db-c6b9-4534-bb76-d8a9e2b436f5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"3ee3a6db-c6b9-4534-bb76-d8a9e2b436f5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551499191069","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:17:06.000+0000"},"publishedAt":"2025-08-18T11:38:27.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:38:29
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:36]  Provided signature: sha256=6fc3eb2b94008f8514b5ec312aad7123c517b1c0a483ddad24abbbf1bae6c1c6
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:37]  Calculated signature: sha256=f28a6f4336ea23c062aa6974901dface1a831e69d466e881fee818bf8d5aa450
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a75363d65cf49345\n    [X-B3-Traceid] => 68a310b3347d1ec575a784a621d14bc1\n    [B3] => 68a310b3347d1ec575a784a621d14bc1-a75363d65cf49345-1\n    [Traceparent] => 00-68a310b3347d1ec575a784a621d14bc1-a75363d65cf49345-01\n    [X-Amzn-Trace-Id] => Root=1-68a310b3-347d1ec575a784a621d14bc1;Parent=a75363d65cf49345;Sampled=1\n    [X-Adsk-Signature] => sha256=6fc3eb2b94008f8514b5ec312aad7123c517b1c0a483ddad24abbbf1bae6c1c6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f00c6206-7d4d-4749-9d5e-68b402c486ee\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:38:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"f00c6206-7d4d-4749-9d5e-68b402c486ee","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:13:16.000+0000"},"publishedAt":"2025-08-18T11:38:27.000Z","csn":"5103159758"}
[webhook] [2025-08-18 11:38:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 11:38:30
[webhook] [2025-08-18 11:38:30] [adwsapi_v2.php:36]  Provided signature: sha256=f28a6f4336ea23c062aa6974901dface1a831e69d466e881fee818bf8d5aa450
[webhook] [2025-08-18 11:38:30] [adwsapi_v2.php:37]  Calculated signature: sha256=f28a6f4336ea23c062aa6974901dface1a831e69d466e881fee818bf8d5aa450
[webhook] [2025-08-18 11:38:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e710f8c764f861d4\n    [X-B3-Traceid] => 68a310b3347d1ec575a784a621d14bc1\n    [B3] => 68a310b3347d1ec575a784a621d14bc1-e710f8c764f861d4-1\n    [Traceparent] => 00-68a310b3347d1ec575a784a621d14bc1-e710f8c764f861d4-01\n    [X-Amzn-Trace-Id] => Root=1-68a310b3-347d1ec575a784a621d14bc1;Parent=e710f8c764f861d4;Sampled=1\n    [X-Adsk-Signature] => sha256=f28a6f4336ea23c062aa6974901dface1a831e69d466e881fee818bf8d5aa450\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f00c6206-7d4d-4749-9d5e-68b402c486ee\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 11:38:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"f00c6206-7d4d-4749-9d5e-68b402c486ee","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T11:13:16.000+0000"},"publishedAt":"2025-08-18T11:38:27.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:12:22
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:36]  Provided signature: sha256=553622516d5da94b8f5f87e4ea0d457250c9cf8848c6adc4fc069636dc82d5e2
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:37]  Calculated signature: sha256=37030efcd57967761c21db4dd26a24c56c3af6afcece3e23e1ffb870e2e4002f
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 89d7001690ab2982\n    [X-B3-Traceid] => 68a342d4347392b47209aa30707c9ae0\n    [B3] => 68a342d4347392b47209aa30707c9ae0-89d7001690ab2982-1\n    [Traceparent] => 00-68a342d4347392b47209aa30707c9ae0-89d7001690ab2982-01\n    [X-Amzn-Trace-Id] => Root=1-68a342d4-347392b47209aa30707c9ae0;Parent=89d7001690ab2982;Sampled=1\n    [X-Adsk-Signature] => sha256=553622516d5da94b8f5f87e4ea0d457250c9cf8848c6adc4fc069636dc82d5e2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c0aa46d8-0525-46ad-ace0-6932fc206b9c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"c0aa46d8-0525-46ad-ace0-6932fc206b9c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","status":"Active","quantity":1,"endDate":"2026-08-18","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T14:57:17.000+0000"},"publishedAt":"2025-08-18T15:12:20.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:12:22
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:36]  Provided signature: sha256=37030efcd57967761c21db4dd26a24c56c3af6afcece3e23e1ffb870e2e4002f
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:37]  Calculated signature: sha256=37030efcd57967761c21db4dd26a24c56c3af6afcece3e23e1ffb870e2e4002f
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6fd8deec67dbca76\n    [X-B3-Traceid] => 68a342d4347392b47209aa30707c9ae0\n    [B3] => 68a342d4347392b47209aa30707c9ae0-6fd8deec67dbca76-1\n    [Traceparent] => 00-68a342d4347392b47209aa30707c9ae0-6fd8deec67dbca76-01\n    [X-Amzn-Trace-Id] => Root=1-68a342d4-347392b47209aa30707c9ae0;Parent=6fd8deec67dbca76;Sampled=1\n    [X-Adsk-Signature] => sha256=37030efcd57967761c21db4dd26a24c56c3af6afcece3e23e1ffb870e2e4002f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c0aa46d8-0525-46ad-ace0-6932fc206b9c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:12:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"c0aa46d8-0525-46ad-ace0-6932fc206b9c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","status":"Active","quantity":1,"endDate":"2026-08-18","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T14:57:17.000+0000"},"publishedAt":"2025-08-18T15:12:20.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:21:08
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:36]  Provided signature: sha256=10f3265cdba63b8b127a8f48cf047aa06e84d5f2f14af3fdfc346e0e6dbfff38
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:37]  Calculated signature: sha256=4477d177ab5b9d48ba3cb0f112d342202659e3d85d05ac12777d23178456ba5d
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 20f8634522736930\n    [X-B3-Traceid] => 68a344e1cd5e6c9baab2352013e90795\n    [B3] => 68a344e1cd5e6c9baab2352013e90795-20f8634522736930-1\n    [Traceparent] => 00-68a344e1cd5e6c9baab2352013e90795-20f8634522736930-01\n    [X-Amzn-Trace-Id] => Root=1-68a344e1-cd5e6c9baab2352013e90795;Parent=20f8634522736930;Sampled=1\n    [X-Adsk-Signature] => sha256=10f3265cdba63b8b127a8f48cf047aa06e84d5f2f14af3fdfc346e0e6dbfff38\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8bbd3b6a-62dd-467c-8f93-199b0eca8236\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"8bbd3b6a-62dd-467c-8f93-199b0eca8236","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1006441","transactionId":"a06ec259-3831-5d80-95fa-a34ea1ca9265","quoteStatus":"Order Submitted","message":"Quote# Q-1006441 status changed to Order Submitted.","modifiedAt":"2025-08-18T15:21:05.668Z"},"publishedAt":"2025-08-18T15:21:06.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:21:08
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:36]  Provided signature: sha256=4477d177ab5b9d48ba3cb0f112d342202659e3d85d05ac12777d23178456ba5d
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:37]  Calculated signature: sha256=4477d177ab5b9d48ba3cb0f112d342202659e3d85d05ac12777d23178456ba5d
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a6fea761f9046010\n    [X-B3-Traceid] => 68a344e1cd5e6c9baab2352013e90795\n    [B3] => 68a344e1cd5e6c9baab2352013e90795-a6fea761f9046010-1\n    [Traceparent] => 00-68a344e1cd5e6c9baab2352013e90795-a6fea761f9046010-01\n    [X-Amzn-Trace-Id] => Root=1-68a344e1-cd5e6c9baab2352013e90795;Parent=a6fea761f9046010;Sampled=1\n    [X-Adsk-Signature] => sha256=4477d177ab5b9d48ba3cb0f112d342202659e3d85d05ac12777d23178456ba5d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8bbd3b6a-62dd-467c-8f93-199b0eca8236\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:21:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"8bbd3b6a-62dd-467c-8f93-199b0eca8236","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1006441","transactionId":"a06ec259-3831-5d80-95fa-a34ea1ca9265","quoteStatus":"Order Submitted","message":"Quote# Q-1006441 status changed to Order Submitted.","modifiedAt":"2025-08-18T15:21:05.668Z"},"publishedAt":"2025-08-18T15:21:06.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:21:10] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:21:10
[webhook] [2025-08-18 15:21:10] [adwsapi_v2.php:36]  Provided signature: sha256=581cba3c7e6e4648c455a94df7409853601b242084244cd232ebdd01c3ac86d1
[webhook] [2025-08-18 15:21:10] [adwsapi_v2.php:37]  Calculated signature: sha256=3735a544b5c69af28514115522aa2d9b10e1df26e8c9c83cc4bcf9676e70d3a2
[webhook] [2025-08-18 15:21:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 29a42cc564946aef\n    [X-B3-Traceid] => 68a344e4dae2ecec85976ffd0f1839f3\n    [B3] => 68a344e4dae2ecec85976ffd0f1839f3-29a42cc564946aef-1\n    [Traceparent] => 00-68a344e4dae2ecec85976ffd0f1839f3-29a42cc564946aef-01\n    [X-Amzn-Trace-Id] => Root=1-68a344e4-dae2ecec85976ffd0f1839f3;Parent=29a42cc564946aef;Sampled=1\n    [X-Adsk-Signature] => sha256=581cba3c7e6e4648c455a94df7409853601b242084244cd232ebdd01c3ac86d1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c46f243c-58f7-43b8-8e1e-830faf47a639\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:21:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"c46f243c-58f7-43b8-8e1e-830faf47a639","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1006441","transactionId":"a06ec259-3831-5d80-95fa-a34ea1ca9265","quoteStatus":"Ordered","message":"Quote# Q-1006441 status changed to Ordered.","modifiedAt":"2025-08-18T15:21:08.335Z"},"publishedAt":"2025-08-18T15:21:08.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:21:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:21:11
[webhook] [2025-08-18 15:21:11] [adwsapi_v2.php:36]  Provided signature: sha256=3735a544b5c69af28514115522aa2d9b10e1df26e8c9c83cc4bcf9676e70d3a2
[webhook] [2025-08-18 15:21:11] [adwsapi_v2.php:37]  Calculated signature: sha256=3735a544b5c69af28514115522aa2d9b10e1df26e8c9c83cc4bcf9676e70d3a2
[webhook] [2025-08-18 15:21:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bd5f7ead00e2463f\n    [X-B3-Traceid] => 68a344e4dae2ecec85976ffd0f1839f3\n    [B3] => 68a344e4dae2ecec85976ffd0f1839f3-bd5f7ead00e2463f-1\n    [Traceparent] => 00-68a344e4dae2ecec85976ffd0f1839f3-bd5f7ead00e2463f-01\n    [X-Amzn-Trace-Id] => Root=1-68a344e4-dae2ecec85976ffd0f1839f3;Parent=bd5f7ead00e2463f;Sampled=1\n    [X-Adsk-Signature] => sha256=3735a544b5c69af28514115522aa2d9b10e1df26e8c9c83cc4bcf9676e70d3a2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c46f243c-58f7-43b8-8e1e-830faf47a639\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:21:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"c46f243c-58f7-43b8-8e1e-830faf47a639","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1006441","transactionId":"a06ec259-3831-5d80-95fa-a34ea1ca9265","quoteStatus":"Ordered","message":"Quote# Q-1006441 status changed to Ordered.","modifiedAt":"2025-08-18T15:21:08.335Z"},"publishedAt":"2025-08-18T15:21:08.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:36:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:36:18
[webhook] [2025-08-18 15:36:18] [adwsapi_v2.php:36]  Provided signature: sha256=e8be854babca77f4a296497b8ac1cd5c1c4fc4137df78a90e3eaccae552d40ce
[webhook] [2025-08-18 15:36:18] [adwsapi_v2.php:37]  Calculated signature: sha256=590bab21cabd4d88f0f2f873d98e28137716c79402585ca5d4bca49ffaeebe9f
[webhook] [2025-08-18 15:36:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f200a6f9c70b60cb\n    [X-B3-Traceid] => 68a3487012c80dde3ca30aa52d808c56\n    [B3] => 68a3487012c80dde3ca30aa52d808c56-f200a6f9c70b60cb-1\n    [Traceparent] => 00-68a3487012c80dde3ca30aa52d808c56-f200a6f9c70b60cb-01\n    [X-Amzn-Trace-Id] => Root=1-68a34870-12c80dde3ca30aa52d808c56;Parent=f200a6f9c70b60cb;Sampled=1\n    [X-Adsk-Signature] => sha256=e8be854babca77f4a296497b8ac1cd5c1c4fc4137df78a90e3eaccae552d40ce\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 12b5e420-e76a-4549-adf3-8e4d7c891ac7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:36:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"12b5e420-e76a-4549-adf3-8e4d7c891ac7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","status":"Active","quantity":1,"endDate":"2026-08-06","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T15:21:13.000+0000"},"publishedAt":"2025-08-18T15:36:16.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:36:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:36:19
[webhook] [2025-08-18 15:36:19] [adwsapi_v2.php:36]  Provided signature: sha256=590bab21cabd4d88f0f2f873d98e28137716c79402585ca5d4bca49ffaeebe9f
[webhook] [2025-08-18 15:36:19] [adwsapi_v2.php:37]  Calculated signature: sha256=590bab21cabd4d88f0f2f873d98e28137716c79402585ca5d4bca49ffaeebe9f
[webhook] [2025-08-18 15:36:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 929ee656371bb763\n    [X-B3-Traceid] => 68a3487012c80dde3ca30aa52d808c56\n    [B3] => 68a3487012c80dde3ca30aa52d808c56-929ee656371bb763-1\n    [Traceparent] => 00-68a3487012c80dde3ca30aa52d808c56-929ee656371bb763-01\n    [X-Amzn-Trace-Id] => Root=1-68a34870-12c80dde3ca30aa52d808c56;Parent=929ee656371bb763;Sampled=1\n    [X-Adsk-Signature] => sha256=590bab21cabd4d88f0f2f873d98e28137716c79402585ca5d4bca49ffaeebe9f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 12b5e420-e76a-4549-adf3-8e4d7c891ac7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:36:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"12b5e420-e76a-4549-adf3-8e4d7c891ac7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","status":"Active","quantity":1,"endDate":"2026-08-06","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-18T15:21:13.000+0000"},"publishedAt":"2025-08-18T15:36:16.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:39:37
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:36]  Provided signature: sha256=0aa12f90586219266ba83afd7dcd1df7ebc00f32c975468a5188eecaa1909b08
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:37]  Calculated signature: sha256=3ed8e2a737ea586c8e1293a1e02e1ae0bf0e67b0256a007066ca42d4817ccc4b
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f70545f1375dae32\n    [X-B3-Traceid] => 68a3493639edb79115925ccd2068954d\n    [B3] => 68a3493639edb79115925ccd2068954d-f70545f1375dae32-1\n    [Traceparent] => 00-68a3493639edb79115925ccd2068954d-f70545f1375dae32-01\n    [X-Amzn-Trace-Id] => Root=1-68a34936-39edb79115925ccd2068954d;Parent=f70545f1375dae32;Sampled=1\n    [X-Adsk-Signature] => sha256=0aa12f90586219266ba83afd7dcd1df7ebc00f32c975468a5188eecaa1909b08\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fea44e86-9cb7-4601-8988-919503533739\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"fea44e86-9cb7-4601-8988-919503533739","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T15:09:27.000+0000"},"publishedAt":"2025-08-18T15:39:34.000Z","csn":"5103159758"}
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 15:39:37
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:36]  Provided signature: sha256=3ed8e2a737ea586c8e1293a1e02e1ae0bf0e67b0256a007066ca42d4817ccc4b
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:37]  Calculated signature: sha256=3ed8e2a737ea586c8e1293a1e02e1ae0bf0e67b0256a007066ca42d4817ccc4b
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7929745cd372c048\n    [X-B3-Traceid] => 68a3493639edb79115925ccd2068954d\n    [B3] => 68a3493639edb79115925ccd2068954d-7929745cd372c048-1\n    [Traceparent] => 00-68a3493639edb79115925ccd2068954d-7929745cd372c048-01\n    [X-Amzn-Trace-Id] => Root=1-68a34936-39edb79115925ccd2068954d;Parent=7929745cd372c048;Sampled=1\n    [X-Adsk-Signature] => sha256=3ed8e2a737ea586c8e1293a1e02e1ae0bf0e67b0256a007066ca42d4817ccc4b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fea44e86-9cb7-4601-8988-919503533739\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 15:39:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"fea44e86-9cb7-4601-8988-919503533739","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T15:09:27.000+0000"},"publishedAt":"2025-08-18T15:39:34.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:03:24
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:36]  Provided signature: sha256=ab6a19bf3a0a5a5ea8e1e44efa78eaf34aa493610cd308a9063227b75b5e9c89
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:37]  Calculated signature: sha256=ab6a19bf3a0a5a5ea8e1e44efa78eaf34aa493610cd308a9063227b75b5e9c89
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f94f6b0c67c896c0\n    [X-B3-Traceid] => 68a34ec9311728820ac9a26d6dee15cb\n    [B3] => 68a34ec9311728820ac9a26d6dee15cb-f94f6b0c67c896c0-1\n    [Traceparent] => 00-68a34ec9311728820ac9a26d6dee15cb-f94f6b0c67c896c0-01\n    [X-Amzn-Trace-Id] => Root=1-68a34ec9-311728820ac9a26d6dee15cb;Parent=f94f6b0c67c896c0;Sampled=1\n    [X-Adsk-Signature] => sha256=ab6a19bf3a0a5a5ea8e1e44efa78eaf34aa493610cd308a9063227b75b5e9c89\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533001504-75551499191069\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533001504-75551499191069","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551499191069","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:03:21.504Z"},"publishedAt":"2025-08-18T16:03:21.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:03:24
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:36]  Provided signature: sha256=90de76cb67903c4eb068291661b3393113b016080a5380507c341231dc4c846b
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:37]  Calculated signature: sha256=ab6a19bf3a0a5a5ea8e1e44efa78eaf34aa493610cd308a9063227b75b5e9c89
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4fb2c98c828abcf9\n    [X-B3-Traceid] => 68a34ec9311728820ac9a26d6dee15cb\n    [B3] => 68a34ec9311728820ac9a26d6dee15cb-4fb2c98c828abcf9-1\n    [Traceparent] => 00-68a34ec9311728820ac9a26d6dee15cb-4fb2c98c828abcf9-01\n    [X-Amzn-Trace-Id] => Root=1-68a34ec9-311728820ac9a26d6dee15cb;Parent=4fb2c98c828abcf9;Sampled=1\n    [X-Adsk-Signature] => sha256=90de76cb67903c4eb068291661b3393113b016080a5380507c341231dc4c846b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533001504-75551499191069\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:03:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533001504-75551499191069","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551499191069","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:03:21.504Z"},"publishedAt":"2025-08-18T16:03:21.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:05:33
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:36]  Provided signature: sha256=95c55a84dd0c259f43b3ef59fdfb28f10f134a7f7805cc2c08fec295c0c7b604
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:37]  Calculated signature: sha256=95c55a84dd0c259f43b3ef59fdfb28f10f134a7f7805cc2c08fec295c0c7b604
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 112bccaa1b18e091\n    [X-B3-Traceid] => 68a34f4b2c2a324a2960a88d5a60ee58\n    [B3] => 68a34f4b2c2a324a2960a88d5a60ee58-112bccaa1b18e091-1\n    [Traceparent] => 00-68a34f4b2c2a324a2960a88d5a60ee58-112bccaa1b18e091-01\n    [X-Amzn-Trace-Id] => Root=1-68a34f4b-2c2a324a2960a88d5a60ee58;Parent=112bccaa1b18e091;Sampled=1\n    [X-Adsk-Signature] => sha256=95c55a84dd0c259f43b3ef59fdfb28f10f134a7f7805cc2c08fec295c0c7b604\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533131062-75551056568699\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533131062-75551056568699","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056568699","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:05:31.062Z"},"publishedAt":"2025-08-18T16:05:31.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:05:33
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:36]  Provided signature: sha256=d45d1480af1e2dc3585ea242994938ef551be89d4cf8db2f3b324442b506fcf5
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:37]  Calculated signature: sha256=95c55a84dd0c259f43b3ef59fdfb28f10f134a7f7805cc2c08fec295c0c7b604
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e806a5e7194d4987\n    [X-B3-Traceid] => 68a34f4b2c2a324a2960a88d5a60ee58\n    [B3] => 68a34f4b2c2a324a2960a88d5a60ee58-e806a5e7194d4987-1\n    [Traceparent] => 00-68a34f4b2c2a324a2960a88d5a60ee58-e806a5e7194d4987-01\n    [X-Amzn-Trace-Id] => Root=1-68a34f4b-2c2a324a2960a88d5a60ee58;Parent=e806a5e7194d4987;Sampled=1\n    [X-Adsk-Signature] => sha256=d45d1480af1e2dc3585ea242994938ef551be89d4cf8db2f3b324442b506fcf5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533131062-75551056568699\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:05:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533131062-75551056568699","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056568699","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:05:31.062Z"},"publishedAt":"2025-08-18T16:05:31.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:09:02
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:36]  Provided signature: sha256=3d95c84a52c6eb4dc4bfd9775686605c06521244760f5ae2e7d797788f7925d2
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:37]  Calculated signature: sha256=3d95c84a52c6eb4dc4bfd9775686605c06521244760f5ae2e7d797788f7925d2
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => efafe57f0beba9ab\n    [X-B3-Traceid] => 68a3501c7eca30534c0dfd225ea97762\n    [B3] => 68a3501c7eca30534c0dfd225ea97762-efafe57f0beba9ab-1\n    [Traceparent] => 00-68a3501c7eca30534c0dfd225ea97762-efafe57f0beba9ab-01\n    [X-Amzn-Trace-Id] => Root=1-68a3501c-7eca30534c0dfd225ea97762;Parent=efafe57f0beba9ab;Sampled=1\n    [X-Adsk-Signature] => sha256=3d95c84a52c6eb4dc4bfd9775686605c06521244760f5ae2e7d797788f7925d2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533340612-59733048340494\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533340612-59733048340494","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:09:00.612Z"},"publishedAt":"2025-08-18T16:09:00.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:09:02
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:36]  Provided signature: sha256=8945d7c7bc6dfad81a351649981cfc67da2429b92719eaa1af5219d6f5a68687
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:37]  Calculated signature: sha256=3d95c84a52c6eb4dc4bfd9775686605c06521244760f5ae2e7d797788f7925d2
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 393b4cabfad29f83\n    [X-B3-Traceid] => 68a3501c7eca30534c0dfd225ea97762\n    [B3] => 68a3501c7eca30534c0dfd225ea97762-393b4cabfad29f83-1\n    [Traceparent] => 00-68a3501c7eca30534c0dfd225ea97762-393b4cabfad29f83-01\n    [X-Amzn-Trace-Id] => Root=1-68a3501c-7eca30534c0dfd225ea97762;Parent=393b4cabfad29f83;Sampled=1\n    [X-Adsk-Signature] => sha256=8945d7c7bc6dfad81a351649981cfc67da2429b92719eaa1af5219d6f5a68687\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533340612-59733048340494\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:09:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533340612-59733048340494","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:09:00.612Z"},"publishedAt":"2025-08-18T16:09:00.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:11:09
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:36]  Provided signature: sha256=c08fae186f9a8737a2ff8e4127e0266922f668dc82426fe7dd2f859367ae3476
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:37]  Calculated signature: sha256=724006861f007a8947b4b8e0706b8eaa0c612ce4fff7ed78c385ef2bad498036
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5ce607b33994537f\n    [X-B3-Traceid] => 68a3509b53978b133c879e3a378fd80e\n    [B3] => 68a3509b53978b133c879e3a378fd80e-5ce607b33994537f-1\n    [Traceparent] => 00-68a3509b53978b133c879e3a378fd80e-5ce607b33994537f-01\n    [X-Amzn-Trace-Id] => Root=1-68a3509b-53978b133c879e3a378fd80e;Parent=5ce607b33994537f;Sampled=1\n    [X-Adsk-Signature] => sha256=c08fae186f9a8737a2ff8e4127e0266922f668dc82426fe7dd2f859367ae3476\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533467125-75551056564098\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533467125-75551056564098","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056564098","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:11:07.125Z"},"publishedAt":"2025-08-18T16:11:07.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:11:09
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:36]  Provided signature: sha256=724006861f007a8947b4b8e0706b8eaa0c612ce4fff7ed78c385ef2bad498036
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:37]  Calculated signature: sha256=724006861f007a8947b4b8e0706b8eaa0c612ce4fff7ed78c385ef2bad498036
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bbec9a93f10a154a\n    [X-B3-Traceid] => 68a3509b53978b133c879e3a378fd80e\n    [B3] => 68a3509b53978b133c879e3a378fd80e-bbec9a93f10a154a-1\n    [Traceparent] => 00-68a3509b53978b133c879e3a378fd80e-bbec9a93f10a154a-01\n    [X-Amzn-Trace-Id] => Root=1-68a3509b-53978b133c879e3a378fd80e;Parent=bbec9a93f10a154a;Sampled=1\n    [X-Adsk-Signature] => sha256=724006861f007a8947b4b8e0706b8eaa0c612ce4fff7ed78c385ef2bad498036\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533467125-75551056564098\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:11:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533467125-75551056564098","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056564098","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:11:07.125Z"},"publishedAt":"2025-08-18T16:11:07.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:11:55
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:36]  Provided signature: sha256=d60e6c7de2d8c06022474c6564f3fa7f85e45a45ac03cbd7a12a2b1df361d18d
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:37]  Calculated signature: sha256=7f02ac19c4f329e3ee069e37289baff82d671ebd59c67b45789cbbc6a99b2b77
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => afe3368406175e93\n    [X-B3-Traceid] => 68a350c9580da8667f7c660a3685ee64\n    [B3] => 68a350c9580da8667f7c660a3685ee64-afe3368406175e93-1\n    [Traceparent] => 00-68a350c9580da8667f7c660a3685ee64-afe3368406175e93-01\n    [X-Amzn-Trace-Id] => Root=1-68a350c9-580da8667f7c660a3685ee64;Parent=afe3368406175e93;Sampled=1\n    [X-Adsk-Signature] => sha256=d60e6c7de2d8c06022474c6564f3fa7f85e45a45ac03cbd7a12a2b1df361d18d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533513427-56992488105316\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533513427-56992488105316","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:11:53.427Z"},"publishedAt":"2025-08-18T16:11:53.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:11:55
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:36]  Provided signature: sha256=7f02ac19c4f329e3ee069e37289baff82d671ebd59c67b45789cbbc6a99b2b77
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:37]  Calculated signature: sha256=7f02ac19c4f329e3ee069e37289baff82d671ebd59c67b45789cbbc6a99b2b77
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8591b24ce3c60f8f\n    [X-B3-Traceid] => 68a350c9580da8667f7c660a3685ee64\n    [B3] => 68a350c9580da8667f7c660a3685ee64-8591b24ce3c60f8f-1\n    [Traceparent] => 00-68a350c9580da8667f7c660a3685ee64-8591b24ce3c60f8f-01\n    [X-Amzn-Trace-Id] => Root=1-68a350c9-580da8667f7c660a3685ee64;Parent=8591b24ce3c60f8f;Sampled=1\n    [X-Adsk-Signature] => sha256=7f02ac19c4f329e3ee069e37289baff82d671ebd59c67b45789cbbc6a99b2b77\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533513427-56992488105316\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:11:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533513427-56992488105316","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:11:53.427Z"},"publishedAt":"2025-08-18T16:11:53.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:13:31
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:36]  Provided signature: sha256=b9118ea1808fb55268016c286955ec5473359cfb273153c84bf98906f6a252bb
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:37]  Calculated signature: sha256=b9118ea1808fb55268016c286955ec5473359cfb273153c84bf98906f6a252bb
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4004f8a40cd89f2e\n    [X-B3-Traceid] => 68a351296ee82b5566d75c1e6f543bbb\n    [B3] => 68a351296ee82b5566d75c1e6f543bbb-4004f8a40cd89f2e-1\n    [Traceparent] => 00-68a351296ee82b5566d75c1e6f543bbb-4004f8a40cd89f2e-01\n    [X-Amzn-Trace-Id] => Root=1-68a35129-6ee82b5566d75c1e6f543bbb;Parent=4004f8a40cd89f2e;Sampled=1\n    [X-Adsk-Signature] => sha256=b9118ea1808fb55268016c286955ec5473359cfb273153c84bf98906f6a252bb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533609154-69460117928610\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533609154-69460117928610","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:13:29.154Z"},"publishedAt":"2025-08-18T16:13:29.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:13:31
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:36]  Provided signature: sha256=057cafe29c1658b8294306b0176da70f9cb86d30e4f42782091cac8541d20f8e
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:37]  Calculated signature: sha256=b9118ea1808fb55268016c286955ec5473359cfb273153c84bf98906f6a252bb
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0a4a3286498a0700\n    [X-B3-Traceid] => 68a351296ee82b5566d75c1e6f543bbb\n    [B3] => 68a351296ee82b5566d75c1e6f543bbb-0a4a3286498a0700-1\n    [Traceparent] => 00-68a351296ee82b5566d75c1e6f543bbb-0a4a3286498a0700-01\n    [X-Amzn-Trace-Id] => Root=1-68a35129-6ee82b5566d75c1e6f543bbb;Parent=0a4a3286498a0700;Sampled=1\n    [X-Adsk-Signature] => sha256=057cafe29c1658b8294306b0176da70f9cb86d30e4f42782091cac8541d20f8e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533609154-69460117928610\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:13:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533609154-69460117928610","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:13:29.154Z"},"publishedAt":"2025-08-18T16:13:29.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:14:29
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:36]  Provided signature: sha256=efb96ca6eb3586872583fa2e1fda874b34bc58b1cbfdd99e4b7cdea3bf8c34b6
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:37]  Calculated signature: sha256=1391691c0ba4bd9aeda929e53888712b5d733c65314bc5bf6372339cb305e907
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1fec5921db7a0083\n    [X-B3-Traceid] => 68a35163611358fb44f25c0d11911d5c\n    [B3] => 68a35163611358fb44f25c0d11911d5c-1fec5921db7a0083-1\n    [Traceparent] => 00-68a35163611358fb44f25c0d11911d5c-1fec5921db7a0083-01\n    [X-Amzn-Trace-Id] => Root=1-68a35163-611358fb44f25c0d11911d5c;Parent=1fec5921db7a0083;Sampled=1\n    [X-Adsk-Signature] => sha256=efb96ca6eb3586872583fa2e1fda874b34bc58b1cbfdd99e4b7cdea3bf8c34b6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533667425-75127504067843\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533667425-75127504067843","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:14:27.425Z"},"publishedAt":"2025-08-18T16:14:27.000Z","csn":"5103159758"}
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 16:14:29
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:36]  Provided signature: sha256=1391691c0ba4bd9aeda929e53888712b5d733c65314bc5bf6372339cb305e907
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:37]  Calculated signature: sha256=1391691c0ba4bd9aeda929e53888712b5d733c65314bc5bf6372339cb305e907
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => aaf8c1675b4cc3b7\n    [X-B3-Traceid] => 68a35163611358fb44f25c0d11911d5c\n    [B3] => 68a35163611358fb44f25c0d11911d5c-aaf8c1675b4cc3b7-1\n    [Traceparent] => 00-68a35163611358fb44f25c0d11911d5c-aaf8c1675b4cc3b7-01\n    [X-Amzn-Trace-Id] => Root=1-68a35163-611358fb44f25c0d11911d5c;Parent=aaf8c1675b4cc3b7;Sampled=1\n    [X-Adsk-Signature] => sha256=1391691c0ba4bd9aeda929e53888712b5d733c65314bc5bf6372339cb305e907\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755533667425-75127504067843\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 16:14:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755533667425-75127504067843","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T16:14:27.425Z"},"publishedAt":"2025-08-18T16:14:27.000Z","csn":"5103159758"}
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 17:40:14
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:36]  Provided signature: sha256=08d107c99813e1d7009441f7d531ffdc9e3315f43798881152b26135cf8006ea
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:37]  Calculated signature: sha256=89fb59a2c86fd829ed489f183ff12151c774f725a364ff5bbeafb89ec048e383
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b834e612c2d376a8\n    [X-B3-Traceid] => 68a3657b41be6eac60f078486992f358\n    [B3] => 68a3657b41be6eac60f078486992f358-b834e612c2d376a8-1\n    [Traceparent] => 00-68a3657b41be6eac60f078486992f358-b834e612c2d376a8-01\n    [X-Amzn-Trace-Id] => Root=1-68a3657b-41be6eac60f078486992f358;Parent=b834e612c2d376a8;Sampled=1\n    [X-Adsk-Signature] => sha256=08d107c99813e1d7009441f7d531ffdc9e3315f43798881152b26135cf8006ea\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9b949871-af90-4ed8-aef2-c21c5609a1e7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"9b949871-af90-4ed8-aef2-c21c5609a1e7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"67116299241327","quantity":6,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T17:05:09.000+0000"},"publishedAt":"2025-08-18T17:40:12.000Z","csn":"5103159758"}
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 17:40:14
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:36]  Provided signature: sha256=89fb59a2c86fd829ed489f183ff12151c774f725a364ff5bbeafb89ec048e383
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:37]  Calculated signature: sha256=89fb59a2c86fd829ed489f183ff12151c774f725a364ff5bbeafb89ec048e383
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e32e6a77275ad570\n    [X-B3-Traceid] => 68a3657b41be6eac60f078486992f358\n    [B3] => 68a3657b41be6eac60f078486992f358-e32e6a77275ad570-1\n    [Traceparent] => 00-68a3657b41be6eac60f078486992f358-e32e6a77275ad570-01\n    [X-Amzn-Trace-Id] => Root=1-68a3657b-41be6eac60f078486992f358;Parent=e32e6a77275ad570;Sampled=1\n    [X-Adsk-Signature] => sha256=89fb59a2c86fd829ed489f183ff12151c774f725a364ff5bbeafb89ec048e383\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9b949871-af90-4ed8-aef2-c21c5609a1e7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 17:40:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"9b949871-af90-4ed8-aef2-c21c5609a1e7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"67116299241327","quantity":6,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T17:05:09.000+0000"},"publishedAt":"2025-08-18T17:40:12.000Z","csn":"5103159758"}
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 19:39:52
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:36]  Provided signature: sha256=f4dee06b702f18b364024b59dc82b402ea4819f93b22fb64efe98642ccf1c593
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:37]  Calculated signature: sha256=12d7ee47e35e631888c3c42c36ae5961de2b462f2db9c4d9ee5ce4d516b6f85b
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 015d7b3ba8788623\n    [X-B3-Traceid] => 68a38185599290756fb0b4ca03453981\n    [B3] => 68a38185599290756fb0b4ca03453981-015d7b3ba8788623-1\n    [Traceparent] => 00-68a38185599290756fb0b4ca03453981-015d7b3ba8788623-01\n    [X-Amzn-Trace-Id] => Root=1-68a38185-599290756fb0b4ca03453981;Parent=015d7b3ba8788623;Sampled=1\n    [X-Adsk-Signature] => sha256=f4dee06b702f18b364024b59dc82b402ea4819f93b22fb64efe98642ccf1c593\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4acda865-a514-4a12-ae8f-fd149441d329\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"4acda865-a514-4a12-ae8f-fd149441d329","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T19:09:42.000+0000"},"publishedAt":"2025-08-18T19:39:49.000Z","csn":"5103159758"}
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 19:39:52
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:36]  Provided signature: sha256=12d7ee47e35e631888c3c42c36ae5961de2b462f2db9c4d9ee5ce4d516b6f85b
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:37]  Calculated signature: sha256=12d7ee47e35e631888c3c42c36ae5961de2b462f2db9c4d9ee5ce4d516b6f85b
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7d7bf7bbe7ddf264\n    [X-B3-Traceid] => 68a38185599290756fb0b4ca03453981\n    [B3] => 68a38185599290756fb0b4ca03453981-7d7bf7bbe7ddf264-1\n    [Traceparent] => 00-68a38185599290756fb0b4ca03453981-7d7bf7bbe7ddf264-01\n    [X-Amzn-Trace-Id] => Root=1-68a38185-599290756fb0b4ca03453981;Parent=7d7bf7bbe7ddf264;Sampled=1\n    [X-Adsk-Signature] => sha256=12d7ee47e35e631888c3c42c36ae5961de2b462f2db9c4d9ee5ce4d516b6f85b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4acda865-a514-4a12-ae8f-fd149441d329\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 19:39:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"4acda865-a514-4a12-ae8f-fd149441d329","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-18T19:09:42.000+0000"},"publishedAt":"2025-08-18T19:39:49.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:05:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:05:49
[webhook] [2025-08-18 20:05:49] [adwsapi_v2.php:36]  Provided signature: sha256=759230100f8b6a130a145a08a5cc34f38c892a9ce18a86d14aaa831deb062999
[webhook] [2025-08-18 20:05:49] [adwsapi_v2.php:37]  Calculated signature: sha256=478fc738f84c7923576f02cd07b8f3cfc52d400aa0bcbe85e429a979698dfed7
[webhook] [2025-08-18 20:05:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e2f2aeb9176f5205\n    [X-B3-Traceid] => 68a3879b45f8b4bf457456f762a1eaef\n    [B3] => 68a3879b45f8b4bf457456f762a1eaef-e2f2aeb9176f5205-1\n    [Traceparent] => 00-68a3879b45f8b4bf457456f762a1eaef-e2f2aeb9176f5205-01\n    [X-Amzn-Trace-Id] => Root=1-68a3879b-45f8b4bf457456f762a1eaef;Parent=e2f2aeb9176f5205;Sampled=1\n    [X-Adsk-Signature] => sha256=759230100f8b6a130a145a08a5cc34f38c892a9ce18a86d14aaa831deb062999\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755547547513-72407332617813\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:05:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755547547513-72407332617813","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:05:47.513Z"},"publishedAt":"2025-08-18T20:05:47.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:05:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:05:50
[webhook] [2025-08-18 20:05:50] [adwsapi_v2.php:36]  Provided signature: sha256=478fc738f84c7923576f02cd07b8f3cfc52d400aa0bcbe85e429a979698dfed7
[webhook] [2025-08-18 20:05:50] [adwsapi_v2.php:37]  Calculated signature: sha256=478fc738f84c7923576f02cd07b8f3cfc52d400aa0bcbe85e429a979698dfed7
[webhook] [2025-08-18 20:05:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b99765cc9fc5d321\n    [X-B3-Traceid] => 68a3879b45f8b4bf457456f762a1eaef\n    [B3] => 68a3879b45f8b4bf457456f762a1eaef-b99765cc9fc5d321-1\n    [Traceparent] => 00-68a3879b45f8b4bf457456f762a1eaef-b99765cc9fc5d321-01\n    [X-Amzn-Trace-Id] => Root=1-68a3879b-45f8b4bf457456f762a1eaef;Parent=b99765cc9fc5d321;Sampled=1\n    [X-Adsk-Signature] => sha256=478fc738f84c7923576f02cd07b8f3cfc52d400aa0bcbe85e429a979698dfed7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755547547513-72407332617813\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:05:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755547547513-72407332617813","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:05:47.513Z"},"publishedAt":"2025-08-18T20:05:47.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:07:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:07:48
[webhook] [2025-08-18 20:07:48] [adwsapi_v2.php:36]  Provided signature: sha256=0200e84a9069df2d77a6b4f651f6e12cda4b4c25ca073bdc8150400cd92a0c22
[webhook] [2025-08-18 20:07:48] [adwsapi_v2.php:37]  Calculated signature: sha256=0200e84a9069df2d77a6b4f651f6e12cda4b4c25ca073bdc8150400cd92a0c22
[webhook] [2025-08-18 20:07:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d5cd4c08388d99e1\n    [X-B3-Traceid] => 68a388121609e25225f9aa60734e685a\n    [B3] => 68a388121609e25225f9aa60734e685a-d5cd4c08388d99e1-1\n    [Traceparent] => 00-68a388121609e25225f9aa60734e685a-d5cd4c08388d99e1-01\n    [X-Amzn-Trace-Id] => Root=1-68a38812-1609e25225f9aa60734e685a;Parent=d5cd4c08388d99e1;Sampled=1\n    [X-Adsk-Signature] => sha256=0200e84a9069df2d77a6b4f651f6e12cda4b4c25ca073bdc8150400cd92a0c22\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755547666544-59679767635256\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:07:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755547666544-59679767635256","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:07:46.544Z"},"publishedAt":"2025-08-18T20:07:46.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:07:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:07:49
[webhook] [2025-08-18 20:07:49] [adwsapi_v2.php:36]  Provided signature: sha256=b07809adae04441865b248e499bfa33638e91747f82f3874a1f82f877a54decf
[webhook] [2025-08-18 20:07:49] [adwsapi_v2.php:37]  Calculated signature: sha256=0200e84a9069df2d77a6b4f651f6e12cda4b4c25ca073bdc8150400cd92a0c22
[webhook] [2025-08-18 20:07:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 55116ef2e3339f0f\n    [X-B3-Traceid] => 68a388121609e25225f9aa60734e685a\n    [B3] => 68a388121609e25225f9aa60734e685a-55116ef2e3339f0f-1\n    [Traceparent] => 00-68a388121609e25225f9aa60734e685a-55116ef2e3339f0f-01\n    [X-Amzn-Trace-Id] => Root=1-68a38812-1609e25225f9aa60734e685a;Parent=55116ef2e3339f0f;Sampled=1\n    [X-Adsk-Signature] => sha256=b07809adae04441865b248e499bfa33638e91747f82f3874a1f82f877a54decf\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755547666544-59679767635256\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:07:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755547666544-59679767635256","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:07:46.544Z"},"publishedAt":"2025-08-18T20:07:46.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:12:30
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:36]  Provided signature: sha256=d36ebf586bb5296bcd5ea261be85f4194bac77b3d0128e0763e21108fb8b647b
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:37]  Calculated signature: sha256=afd1f3c5dd0798328d517a3d0e400e77b4913c5ac1a6e293cf9659742161c220
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0ade0a533fd578df\n    [X-B3-Traceid] => 68a3892c7adf83952d696c7171da2cd2\n    [B3] => 68a3892c7adf83952d696c7171da2cd2-0ade0a533fd578df-1\n    [Traceparent] => 00-68a3892c7adf83952d696c7171da2cd2-0ade0a533fd578df-01\n    [X-Amzn-Trace-Id] => Root=1-68a3892c-7adf83952d696c7171da2cd2;Parent=0ade0a533fd578df;Sampled=1\n    [X-Adsk-Signature] => sha256=d36ebf586bb5296bcd5ea261be85f4194bac77b3d0128e0763e21108fb8b647b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755547948100-75551056564098\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755547948100-75551056564098","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056564098","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:12:28.100Z"},"publishedAt":"2025-08-18T20:12:28.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:12:30
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:36]  Provided signature: sha256=afd1f3c5dd0798328d517a3d0e400e77b4913c5ac1a6e293cf9659742161c220
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:37]  Calculated signature: sha256=afd1f3c5dd0798328d517a3d0e400e77b4913c5ac1a6e293cf9659742161c220
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 97189d998a869430\n    [X-B3-Traceid] => 68a3892c7adf83952d696c7171da2cd2\n    [B3] => 68a3892c7adf83952d696c7171da2cd2-97189d998a869430-1\n    [Traceparent] => 00-68a3892c7adf83952d696c7171da2cd2-97189d998a869430-01\n    [X-Amzn-Trace-Id] => Root=1-68a3892c-7adf83952d696c7171da2cd2;Parent=97189d998a869430;Sampled=1\n    [X-Adsk-Signature] => sha256=afd1f3c5dd0798328d517a3d0e400e77b4913c5ac1a6e293cf9659742161c220\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755547948100-75551056564098\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:12:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755547948100-75551056564098","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056564098","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:12:28.100Z"},"publishedAt":"2025-08-18T20:12:28.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:14:32
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:36]  Provided signature: sha256=88052274050e4c1aeb505a45675af86885eb1d1b5d068d9f393f037e7075b55b
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:37]  Calculated signature: sha256=88052274050e4c1aeb505a45675af86885eb1d1b5d068d9f393f037e7075b55b
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6ea3d03206abcd88\n    [X-B3-Traceid] => 68a389a670d148912f70ad6b43d95371\n    [B3] => 68a389a670d148912f70ad6b43d95371-6ea3d03206abcd88-1\n    [Traceparent] => 00-68a389a670d148912f70ad6b43d95371-6ea3d03206abcd88-01\n    [X-Amzn-Trace-Id] => Root=1-68a389a6-70d148912f70ad6b43d95371;Parent=6ea3d03206abcd88;Sampled=1\n    [X-Adsk-Signature] => sha256=88052274050e4c1aeb505a45675af86885eb1d1b5d068d9f393f037e7075b55b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548070647-75551056568699\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548070647-75551056568699","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056568699","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:14:30.647Z"},"publishedAt":"2025-08-18T20:14:30.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:14:32
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:36]  Provided signature: sha256=bfc2d6cc86f9aebad74ac36590dd037e7c7533b772b34f7698dd679b47356c95
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:37]  Calculated signature: sha256=88052274050e4c1aeb505a45675af86885eb1d1b5d068d9f393f037e7075b55b
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fe8d2064df5a6cff\n    [X-B3-Traceid] => 68a389a670d148912f70ad6b43d95371\n    [B3] => 68a389a670d148912f70ad6b43d95371-fe8d2064df5a6cff-1\n    [Traceparent] => 00-68a389a670d148912f70ad6b43d95371-fe8d2064df5a6cff-01\n    [X-Amzn-Trace-Id] => Root=1-68a389a6-70d148912f70ad6b43d95371;Parent=fe8d2064df5a6cff;Sampled=1\n    [X-Adsk-Signature] => sha256=bfc2d6cc86f9aebad74ac36590dd037e7c7533b772b34f7698dd679b47356c95\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548070647-75551056568699\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:14:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548070647-75551056568699","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551056568699","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:14:30.647Z"},"publishedAt":"2025-08-18T20:14:30.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:15:33
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:36]  Provided signature: sha256=4d966b9a007674da55ca5495933b93ff609d98a8291b0edf18a0795b8549183e
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:37]  Calculated signature: sha256=407926c49c360ebf59bc64cf9b9716fc024c043c5d0fbf265801d7f8fefcc707
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9d66a97a9e049dbe\n    [X-B3-Traceid] => 68a389e226e1b5407b2a151124f78da4\n    [B3] => 68a389e226e1b5407b2a151124f78da4-9d66a97a9e049dbe-1\n    [Traceparent] => 00-68a389e226e1b5407b2a151124f78da4-9d66a97a9e049dbe-01\n    [X-Amzn-Trace-Id] => Root=1-68a389e2-26e1b5407b2a151124f78da4;Parent=9d66a97a9e049dbe;Sampled=1\n    [X-Adsk-Signature] => sha256=4d966b9a007674da55ca5495933b93ff609d98a8291b0edf18a0795b8549183e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548130713-56992488105316\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548130713-56992488105316","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:15:30.713Z"},"publishedAt":"2025-08-18T20:15:30.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:15:33
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:36]  Provided signature: sha256=407926c49c360ebf59bc64cf9b9716fc024c043c5d0fbf265801d7f8fefcc707
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:37]  Calculated signature: sha256=407926c49c360ebf59bc64cf9b9716fc024c043c5d0fbf265801d7f8fefcc707
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cc80ac9677e4d264\n    [X-B3-Traceid] => 68a389e226e1b5407b2a151124f78da4\n    [B3] => 68a389e226e1b5407b2a151124f78da4-cc80ac9677e4d264-1\n    [Traceparent] => 00-68a389e226e1b5407b2a151124f78da4-cc80ac9677e4d264-01\n    [X-Amzn-Trace-Id] => Root=1-68a389e2-26e1b5407b2a151124f78da4;Parent=cc80ac9677e4d264;Sampled=1\n    [X-Adsk-Signature] => sha256=407926c49c360ebf59bc64cf9b9716fc024c043c5d0fbf265801d7f8fefcc707\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548130713-56992488105316\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:15:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548130713-56992488105316","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56992488105316","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:15:30.713Z"},"publishedAt":"2025-08-18T20:15:30.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:17:46
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:36]  Provided signature: sha256=8ed850447f4f8eaccf111249879fca70fc4f3b3f92c78de38424038252cab7bd
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:37]  Calculated signature: sha256=90165e53a0b6b6eb55d3074d25355e997406179fa427810570f78141e9444438
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b3f6cb8b03348549\n    [X-B3-Traceid] => 68a38a68302d79ec2a39f91134b0c717\n    [B3] => 68a38a68302d79ec2a39f91134b0c717-b3f6cb8b03348549-1\n    [Traceparent] => 00-68a38a68302d79ec2a39f91134b0c717-b3f6cb8b03348549-01\n    [X-Amzn-Trace-Id] => Root=1-68a38a68-302d79ec2a39f91134b0c717;Parent=b3f6cb8b03348549;Sampled=1\n    [X-Adsk-Signature] => sha256=8ed850447f4f8eaccf111249879fca70fc4f3b3f92c78de38424038252cab7bd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548264263-59733048340494\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548264263-59733048340494","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:17:44.263Z"},"publishedAt":"2025-08-18T20:17:44.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:17:46
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:36]  Provided signature: sha256=90165e53a0b6b6eb55d3074d25355e997406179fa427810570f78141e9444438
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:37]  Calculated signature: sha256=90165e53a0b6b6eb55d3074d25355e997406179fa427810570f78141e9444438
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 94211d7bfcc26eb6\n    [X-B3-Traceid] => 68a38a68302d79ec2a39f91134b0c717\n    [B3] => 68a38a68302d79ec2a39f91134b0c717-94211d7bfcc26eb6-1\n    [Traceparent] => 00-68a38a68302d79ec2a39f91134b0c717-94211d7bfcc26eb6-01\n    [X-Amzn-Trace-Id] => Root=1-68a38a68-302d79ec2a39f91134b0c717;Parent=94211d7bfcc26eb6;Sampled=1\n    [X-Adsk-Signature] => sha256=90165e53a0b6b6eb55d3074d25355e997406179fa427810570f78141e9444438\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548264263-59733048340494\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:17:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548264263-59733048340494","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59733048340494","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:17:44.263Z"},"publishedAt":"2025-08-18T20:17:44.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:20:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:20:17
[webhook] [2025-08-18 20:20:17] [adwsapi_v2.php:36]  Provided signature: sha256=d9861c8648fe16f6f6810dc1280d3fb0b65047f7e6b17e17b5f3c6202c5faf04
[webhook] [2025-08-18 20:20:17] [adwsapi_v2.php:37]  Calculated signature: sha256=994bc341f7fd33d40abdf101807cc18f6e7d29246788d5251d1a24762e158087
[webhook] [2025-08-18 20:20:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2d0ad3fef5620597\n    [X-B3-Traceid] => 68a38aff4d8affd577c3ed795e659f33\n    [B3] => 68a38aff4d8affd577c3ed795e659f33-2d0ad3fef5620597-1\n    [Traceparent] => 00-68a38aff4d8affd577c3ed795e659f33-2d0ad3fef5620597-01\n    [X-Amzn-Trace-Id] => Root=1-68a38aff-4d8affd577c3ed795e659f33;Parent=2d0ad3fef5620597;Sampled=1\n    [X-Adsk-Signature] => sha256=d9861c8648fe16f6f6810dc1280d3fb0b65047f7e6b17e17b5f3c6202c5faf04\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548415372-75551499191069\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:20:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548415372-75551499191069","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551499191069","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:20:15.372Z"},"publishedAt":"2025-08-18T20:20:15.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:20:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:20:18
[webhook] [2025-08-18 20:20:18] [adwsapi_v2.php:36]  Provided signature: sha256=994bc341f7fd33d40abdf101807cc18f6e7d29246788d5251d1a24762e158087
[webhook] [2025-08-18 20:20:18] [adwsapi_v2.php:37]  Calculated signature: sha256=994bc341f7fd33d40abdf101807cc18f6e7d29246788d5251d1a24762e158087
[webhook] [2025-08-18 20:20:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 16dda6a440fd2ca4\n    [X-B3-Traceid] => 68a38aff4d8affd577c3ed795e659f33\n    [B3] => 68a38aff4d8affd577c3ed795e659f33-16dda6a440fd2ca4-1\n    [Traceparent] => 00-68a38aff4d8affd577c3ed795e659f33-16dda6a440fd2ca4-01\n    [X-Amzn-Trace-Id] => Root=1-68a38aff-4d8affd577c3ed795e659f33;Parent=16dda6a440fd2ca4;Sampled=1\n    [X-Adsk-Signature] => sha256=994bc341f7fd33d40abdf101807cc18f6e7d29246788d5251d1a24762e158087\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548415372-75551499191069\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:20:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548415372-75551499191069","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75551499191069","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:20:15.372Z"},"publishedAt":"2025-08-18T20:20:15.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:24:48
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:36]  Provided signature: sha256=f0addf4ef361c937ac652427c5b97452cd1c0522401ddf4ac199d40e24d37179
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:37]  Calculated signature: sha256=f0addf4ef361c937ac652427c5b97452cd1c0522401ddf4ac199d40e24d37179
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f097d5accaab05d3\n    [X-B3-Traceid] => 68a38c0d39ad32f6651b66d167a5dfc4\n    [B3] => 68a38c0d39ad32f6651b66d167a5dfc4-f097d5accaab05d3-1\n    [Traceparent] => 00-68a38c0d39ad32f6651b66d167a5dfc4-f097d5accaab05d3-01\n    [X-Amzn-Trace-Id] => Root=1-68a38c0d-39ad32f6651b66d167a5dfc4;Parent=f097d5accaab05d3;Sampled=1\n    [X-Adsk-Signature] => sha256=f0addf4ef361c937ac652427c5b97452cd1c0522401ddf4ac199d40e24d37179\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548685764-69460117928610\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548685764-69460117928610","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:24:45.764Z"},"publishedAt":"2025-08-18T20:24:45.000Z","csn":"5103159758"}
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 20:24:48
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:36]  Provided signature: sha256=ca7efcb159155e4c6c24a9e9aee64ba74333d3dfb7658bd3b2290634fdd1606f
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:37]  Calculated signature: sha256=f0addf4ef361c937ac652427c5b97452cd1c0522401ddf4ac199d40e24d37179
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8c62d5809410d8e9\n    [X-B3-Traceid] => 68a38c0d39ad32f6651b66d167a5dfc4\n    [B3] => 68a38c0d39ad32f6651b66d167a5dfc4-8c62d5809410d8e9-1\n    [Traceparent] => 00-68a38c0d39ad32f6651b66d167a5dfc4-8c62d5809410d8e9-01\n    [X-Amzn-Trace-Id] => Root=1-68a38c0d-39ad32f6651b66d167a5dfc4;Parent=8c62d5809410d8e9;Sampled=1\n    [X-Adsk-Signature] => sha256=ca7efcb159155e4c6c24a9e9aee64ba74333d3dfb7658bd3b2290634fdd1606f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755548685764-69460117928610\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 20:24:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755548685764-69460117928610","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69460117928610","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T20:24:45.764Z"},"publishedAt":"2025-08-18T20:24:45.000Z","csn":"5103159758"}
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 22:07:22
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:36]  Provided signature: sha256=fd8f4308c194ca44c7bdd89b0414ca5692b55561c663d00cc6750861923ed574
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:37]  Calculated signature: sha256=75e027db834859b24ea7528f28d641929e02edea9e9d4caafc8e3b7721041b2a
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6952bd133d0c5976\n    [X-B3-Traceid] => 68a3a4186452f7b31f33ac5d1d48a5ed\n    [B3] => 68a3a4186452f7b31f33ac5d1d48a5ed-6952bd133d0c5976-1\n    [Traceparent] => 00-68a3a4186452f7b31f33ac5d1d48a5ed-6952bd133d0c5976-01\n    [X-Amzn-Trace-Id] => Root=1-68a3a418-6452f7b31f33ac5d1d48a5ed;Parent=6952bd133d0c5976;Sampled=1\n    [X-Adsk-Signature] => sha256=fd8f4308c194ca44c7bdd89b0414ca5692b55561c663d00cc6750861923ed574\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755554840488-72407332617813\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755554840488-72407332617813","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T22:07:20.488Z"},"publishedAt":"2025-08-18T22:07:20.000Z","csn":"5103159758"}
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 22:07:22
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:36]  Provided signature: sha256=75e027db834859b24ea7528f28d641929e02edea9e9d4caafc8e3b7721041b2a
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:37]  Calculated signature: sha256=75e027db834859b24ea7528f28d641929e02edea9e9d4caafc8e3b7721041b2a
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 77f36eb937dc7d92\n    [X-B3-Traceid] => 68a3a4186452f7b31f33ac5d1d48a5ed\n    [B3] => 68a3a4186452f7b31f33ac5d1d48a5ed-77f36eb937dc7d92-1\n    [Traceparent] => 00-68a3a4186452f7b31f33ac5d1d48a5ed-77f36eb937dc7d92-01\n    [X-Amzn-Trace-Id] => Root=1-68a3a418-6452f7b31f33ac5d1d48a5ed;Parent=77f36eb937dc7d92;Sampled=1\n    [X-Adsk-Signature] => sha256=75e027db834859b24ea7528f28d641929e02edea9e9d4caafc8e3b7721041b2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755554840488-72407332617813\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 22:07:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755554840488-72407332617813","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72407332617813","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T22:07:20.488Z"},"publishedAt":"2025-08-18T22:07:20.000Z","csn":"5103159758"}
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 22:10:31
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:36]  Provided signature: sha256=fe6e2f84e8f63a481361b7edf7549692332c6dd80a4b168eca33626785465931
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:37]  Calculated signature: sha256=fe6e2f84e8f63a481361b7edf7549692332c6dd80a4b168eca33626785465931
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8dfd7685b02b4f77\n    [X-B3-Traceid] => 68a3a4d40a093cd939632fb0317c8438\n    [B3] => 68a3a4d40a093cd939632fb0317c8438-8dfd7685b02b4f77-1\n    [Traceparent] => 00-68a3a4d40a093cd939632fb0317c8438-8dfd7685b02b4f77-01\n    [X-Amzn-Trace-Id] => Root=1-68a3a4d4-0a093cd939632fb0317c8438;Parent=8dfd7685b02b4f77;Sampled=1\n    [X-Adsk-Signature] => sha256=fe6e2f84e8f63a481361b7edf7549692332c6dd80a4b168eca33626785465931\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755555028741-59679767635256\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755555028741-59679767635256","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T22:10:28.741Z"},"publishedAt":"2025-08-18T22:10:28.000Z","csn":"5103159758"}
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 22:10:31
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:36]  Provided signature: sha256=61c2a9b07c281bc5894a48db80ec575ce78909f8d4ce68cc9f174424e81bb5f0
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:37]  Calculated signature: sha256=fe6e2f84e8f63a481361b7edf7549692332c6dd80a4b168eca33626785465931
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 65bf649d386d398a\n    [X-B3-Traceid] => 68a3a4d40a093cd939632fb0317c8438\n    [B3] => 68a3a4d40a093cd939632fb0317c8438-65bf649d386d398a-1\n    [Traceparent] => 00-68a3a4d40a093cd939632fb0317c8438-65bf649d386d398a-01\n    [X-Amzn-Trace-Id] => Root=1-68a3a4d4-0a093cd939632fb0317c8438;Parent=65bf649d386d398a;Sampled=1\n    [X-Adsk-Signature] => sha256=61c2a9b07c281bc5894a48db80ec575ce78909f8d4ce68cc9f174424e81bb5f0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755555028741-59679767635256\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 22:10:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755555028741-59679767635256","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59679767635256","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-18T22:10:28.741Z"},"publishedAt":"2025-08-18T22:10:28.000Z","csn":"5103159758"}
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 23:01:53
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:36]  Provided signature: sha256=3de1f7fea080234eb3e7372f4c815c7f6d7dcbc073cecb4bdfe4dd403d1b8380
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:37]  Calculated signature: sha256=214717263906c6f6d659795124f86b5c90ed0c0d6c2009b10865fad1a4f73a56
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 118f0399792d5500\n    [X-B3-Traceid] => 68a3b0de188048e8231406684a436b7a\n    [B3] => 68a3b0de188048e8231406684a436b7a-118f0399792d5500-1\n    [Traceparent] => 00-68a3b0de188048e8231406684a436b7a-118f0399792d5500-01\n    [X-Amzn-Trace-Id] => Root=1-68a3b0de-188048e8231406684a436b7a;Parent=118f0399792d5500;Sampled=1\n    [X-Adsk-Signature] => sha256=3de1f7fea080234eb3e7372f4c815c7f6d7dcbc073cecb4bdfe4dd403d1b8380\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755558110751-572-31811921\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755558110751-572-31811921","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"572-31811921","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-18T23:01:50.751Z"},"publishedAt":"2025-08-18T23:01:50.000Z","csn":"5103159758"}
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-18 23:01:53
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:36]  Provided signature: sha256=214717263906c6f6d659795124f86b5c90ed0c0d6c2009b10865fad1a4f73a56
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:37]  Calculated signature: sha256=214717263906c6f6d659795124f86b5c90ed0c0d6c2009b10865fad1a4f73a56
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0bb3772c06be86a5\n    [X-B3-Traceid] => 68a3b0de188048e8231406684a436b7a\n    [B3] => 68a3b0de188048e8231406684a436b7a-0bb3772c06be86a5-1\n    [Traceparent] => 00-68a3b0de188048e8231406684a436b7a-0bb3772c06be86a5-01\n    [X-Amzn-Trace-Id] => Root=1-68a3b0de-188048e8231406684a436b7a;Parent=0bb3772c06be86a5;Sampled=1\n    [X-Adsk-Signature] => sha256=214717263906c6f6d659795124f86b5c90ed0c0d6c2009b10865fad1a4f73a56\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1755558110751-572-31811921\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-18 23:01:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"1755558110751-572-31811921","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"572-31811921","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-18T23:01:50.751Z"},"publishedAt":"2025-08-18T23:01:50.000Z","csn":"5103159758"}
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 00:00:42
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:36]  Provided signature: sha256=84ca115fe882997821ea515db7b6728410f0cd05fa6cdf1cb817241f4576f508
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:37]  Calculated signature: sha256=07d7ba0070d6c2b44336806c98495295d152cbaf3f1cb04523bd1297a49574ab
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7054d4ce0aa9910c\n    [X-B3-Traceid] => 68a3bea7715d7bc015df88f17831ea22\n    [B3] => 68a3bea7715d7bc015df88f17831ea22-7054d4ce0aa9910c-1\n    [Traceparent] => 00-68a3bea7715d7bc015df88f17831ea22-7054d4ce0aa9910c-01\n    [X-Amzn-Trace-Id] => Root=1-68a3bea7-715d7bc015df88f17831ea22;Parent=7054d4ce0aa9910c;Sampled=1\n    [X-Adsk-Signature] => sha256=84ca115fe882997821ea515db7b6728410f0cd05fa6cdf1cb817241f4576f508\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ac166794-962b-42e1-8427-d9eba1a931ca\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"ac166794-962b-42e1-8427-d9eba1a931ca","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-19T00:00:39Z"},"publishedAt":"2025-08-19T00:00:39.000Z","country":"GB"}
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 00:00:42
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:36]  Provided signature: sha256=07d7ba0070d6c2b44336806c98495295d152cbaf3f1cb04523bd1297a49574ab
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:37]  Calculated signature: sha256=07d7ba0070d6c2b44336806c98495295d152cbaf3f1cb04523bd1297a49574ab
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e27d3db716d63c1c\n    [X-B3-Traceid] => 68a3bea7715d7bc015df88f17831ea22\n    [B3] => 68a3bea7715d7bc015df88f17831ea22-e27d3db716d63c1c-1\n    [Traceparent] => 00-68a3bea7715d7bc015df88f17831ea22-e27d3db716d63c1c-01\n    [X-Amzn-Trace-Id] => Root=1-68a3bea7-715d7bc015df88f17831ea22;Parent=e27d3db716d63c1c;Sampled=1\n    [X-Adsk-Signature] => sha256=07d7ba0070d6c2b44336806c98495295d152cbaf3f1cb04523bd1297a49574ab\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ac166794-962b-42e1-8427-d9eba1a931ca\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 00:00:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"ac166794-962b-42e1-8427-d9eba1a931ca","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-19T00:00:39Z"},"publishedAt":"2025-08-19T00:00:39.000Z","country":"GB"}
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 07:09:41
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:36]  Provided signature: sha256=db05d5aac26bc3cd4e77bd0fc3865fcdf01990bdb7d247df8d6306cb5eaf3d4e
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:37]  Calculated signature: sha256=788eae225642aa2091cd367bbde4bd3c3447005cfa62cda207ad4f1706799576
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7353d62f3afc06b3\n    [X-B3-Traceid] => 68a4233362cbb29d7277cfe04ffc5d95\n    [B3] => 68a4233362cbb29d7277cfe04ffc5d95-7353d62f3afc06b3-1\n    [Traceparent] => 00-68a4233362cbb29d7277cfe04ffc5d95-7353d62f3afc06b3-01\n    [X-Amzn-Trace-Id] => Root=1-68a42333-62cbb29d7277cfe04ffc5d95;Parent=7353d62f3afc06b3;Sampled=1\n    [X-Adsk-Signature] => sha256=db05d5aac26bc3cd4e77bd0fc3865fcdf01990bdb7d247df8d6306cb5eaf3d4e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1d481657-68d5-43e7-be3d-483356c07a96\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 07:09:41
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1d481657-68d5-43e7-be3d-483356c07a96","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56577398248377","quantity":4,"message":"subscription quantity changed.","modifiedAt":"2025-08-19T06:44:37.000+0000"},"publishedAt":"2025-08-19T07:09:39.000Z","csn":"5103159758"}
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:36]  Provided signature: sha256=788eae225642aa2091cd367bbde4bd3c3447005cfa62cda207ad4f1706799576
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:37]  Calculated signature: sha256=788eae225642aa2091cd367bbde4bd3c3447005cfa62cda207ad4f1706799576
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e597fa26b3e6c68a\n    [X-B3-Traceid] => 68a4233362cbb29d7277cfe04ffc5d95\n    [B3] => 68a4233362cbb29d7277cfe04ffc5d95-e597fa26b3e6c68a-1\n    [Traceparent] => 00-68a4233362cbb29d7277cfe04ffc5d95-e597fa26b3e6c68a-01\n    [X-Amzn-Trace-Id] => Root=1-68a42333-62cbb29d7277cfe04ffc5d95;Parent=e597fa26b3e6c68a;Sampled=1\n    [X-Adsk-Signature] => sha256=788eae225642aa2091cd367bbde4bd3c3447005cfa62cda207ad4f1706799576\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1d481657-68d5-43e7-be3d-483356c07a96\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 07:09:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1d481657-68d5-43e7-be3d-483356c07a96","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56577398248377","quantity":4,"message":"subscription quantity changed.","modifiedAt":"2025-08-19T06:44:37.000+0000"},"publishedAt":"2025-08-19T07:09:39.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:25:27
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:36]  Provided signature: sha256=0dfc53f10d71da82287df021d9651d59e1ea21fc9a955aa02f60aa08065aa4f3
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:37]  Calculated signature: sha256=2203677f31e5e59a5b77f3611e57711e1587d020c6757182917449709ac92c12
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 62a9cc42958df7a4\n    [X-B3-Traceid] => 68a434f4c904afebe0281f7fb7e89cd4\n    [B3] => 68a434f4c904afebe0281f7fb7e89cd4-62a9cc42958df7a4-1\n    [Traceparent] => 00-68a434f4c904afebe0281f7fb7e89cd4-62a9cc42958df7a4-01\n    [X-Amzn-Trace-Id] => Root=1-68a434f4-c904afebe0281f7fb7e89cd4;Parent=62a9cc42958df7a4;Sampled=1\n    [X-Adsk-Signature] => sha256=0dfc53f10d71da82287df021d9651d59e1ea21fc9a955aa02f60aa08065aa4f3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 17ada605-702e-485d-94d4-da6bca7c9323\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"17ada605-702e-485d-94d4-da6bca7c9323","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Draft","message":"Quote# Q-1015216 status changed to Draft.","modifiedAt":"2025-08-19T08:25:24.683Z"},"publishedAt":"2025-08-19T08:25:24.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:25:27
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:36]  Provided signature: sha256=2203677f31e5e59a5b77f3611e57711e1587d020c6757182917449709ac92c12
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:37]  Calculated signature: sha256=2203677f31e5e59a5b77f3611e57711e1587d020c6757182917449709ac92c12
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 30000cc945801591\n    [X-B3-Traceid] => 68a434f4c904afebe0281f7fb7e89cd4\n    [B3] => 68a434f4c904afebe0281f7fb7e89cd4-30000cc945801591-1\n    [Traceparent] => 00-68a434f4c904afebe0281f7fb7e89cd4-30000cc945801591-01\n    [X-Amzn-Trace-Id] => Root=1-68a434f4-c904afebe0281f7fb7e89cd4;Parent=30000cc945801591;Sampled=1\n    [X-Adsk-Signature] => sha256=2203677f31e5e59a5b77f3611e57711e1587d020c6757182917449709ac92c12\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 17ada605-702e-485d-94d4-da6bca7c9323\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:25:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"17ada605-702e-485d-94d4-da6bca7c9323","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Draft","message":"Quote# Q-1015216 status changed to Draft.","modifiedAt":"2025-08-19T08:25:24.683Z"},"publishedAt":"2025-08-19T08:25:24.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:27:39
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:36]  Provided signature: sha256=2f4b1488ba82d75404bdc96b4bc96814dfa5f84d78e462f0d9752ed7e83d0fcc
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:37]  Calculated signature: sha256=2f4b1488ba82d75404bdc96b4bc96814dfa5f84d78e462f0d9752ed7e83d0fcc
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d6be0e15a560bbd7\n    [X-B3-Traceid] => 68a43578c7e3e8070928d16170c7f4d9\n    [B3] => 68a43578c7e3e8070928d16170c7f4d9-d6be0e15a560bbd7-1\n    [Traceparent] => 00-68a43578c7e3e8070928d16170c7f4d9-d6be0e15a560bbd7-01\n    [X-Amzn-Trace-Id] => Root=1-68a43578-c7e3e8070928d16170c7f4d9;Parent=d6be0e15a560bbd7;Sampled=1\n    [X-Adsk-Signature] => sha256=2f4b1488ba82d75404bdc96b4bc96814dfa5f84d78e462f0d9752ed7e83d0fcc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 08134ee8-0697-4ea7-9cb7-9cad05646425\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"08134ee8-0697-4ea7-9cb7-9cad05646425","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Quoted","message":"Quote# Q-1015216 status changed to Quoted.","modifiedAt":"2025-08-19T08:27:36.547Z"},"publishedAt":"2025-08-19T08:27:36.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:27:39
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:36]  Provided signature: sha256=d689b69823899f2e656a2e77c5b31ed8bce01f00f5ef1eb5fb48bcce5c471c5a
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:37]  Calculated signature: sha256=2f4b1488ba82d75404bdc96b4bc96814dfa5f84d78e462f0d9752ed7e83d0fcc
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cf0188777e44824b\n    [X-B3-Traceid] => 68a43578c7e3e8070928d16170c7f4d9\n    [B3] => 68a43578c7e3e8070928d16170c7f4d9-cf0188777e44824b-1\n    [Traceparent] => 00-68a43578c7e3e8070928d16170c7f4d9-cf0188777e44824b-01\n    [X-Amzn-Trace-Id] => Root=1-68a43578-c7e3e8070928d16170c7f4d9;Parent=cf0188777e44824b;Sampled=1\n    [X-Adsk-Signature] => sha256=d689b69823899f2e656a2e77c5b31ed8bce01f00f5ef1eb5fb48bcce5c471c5a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 08134ee8-0697-4ea7-9cb7-9cad05646425\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:27:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"08134ee8-0697-4ea7-9cb7-9cad05646425","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Quoted","message":"Quote# Q-1015216 status changed to Quoted.","modifiedAt":"2025-08-19T08:27:36.547Z"},"publishedAt":"2025-08-19T08:27:36.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:30:51
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:36]  Provided signature: sha256=15cdcdd15221d2897110380b0ec4bccbd4ba7d5d29e71ddc86f11d7cd363ff60
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:37]  Calculated signature: sha256=15cdcdd15221d2897110380b0ec4bccbd4ba7d5d29e71ddc86f11d7cd363ff60
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b639a94aabd37563\n    [X-B3-Traceid] => 68a43639bc6009de833d82e66f3df07e\n    [B3] => 68a43639bc6009de833d82e66f3df07e-b639a94aabd37563-1\n    [Traceparent] => 00-68a43639bc6009de833d82e66f3df07e-b639a94aabd37563-01\n    [X-Amzn-Trace-Id] => Root=1-68a43639-bc6009de833d82e66f3df07e;Parent=b639a94aabd37563;Sampled=1\n    [X-Adsk-Signature] => sha256=15cdcdd15221d2897110380b0ec4bccbd4ba7d5d29e71ddc86f11d7cd363ff60\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 47ad5a68-ff32-4e21-a5be-5d9d822e3a34\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"47ad5a68-ff32-4e21-a5be-5d9d822e3a34","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015236","transactionId":"283342fb-4686-514f-a764-23e57ee93b8e","quoteStatus":"Draft","message":"Quote# Q-1015236 status changed to Draft.","modifiedAt":"2025-08-19T08:30:48.979Z"},"publishedAt":"2025-08-19T08:30:49.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:30:51
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:36]  Provided signature: sha256=457a2e1e2a156ad3aa517465436e91c396a0cb88afe2164efd2c83dcb1536036
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:37]  Calculated signature: sha256=15cdcdd15221d2897110380b0ec4bccbd4ba7d5d29e71ddc86f11d7cd363ff60
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d82554556f10758f\n    [X-B3-Traceid] => 68a43639bc6009de833d82e66f3df07e\n    [B3] => 68a43639bc6009de833d82e66f3df07e-d82554556f10758f-1\n    [Traceparent] => 00-68a43639bc6009de833d82e66f3df07e-d82554556f10758f-01\n    [X-Amzn-Trace-Id] => Root=1-68a43639-bc6009de833d82e66f3df07e;Parent=d82554556f10758f;Sampled=1\n    [X-Adsk-Signature] => sha256=457a2e1e2a156ad3aa517465436e91c396a0cb88afe2164efd2c83dcb1536036\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 47ad5a68-ff32-4e21-a5be-5d9d822e3a34\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:30:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"47ad5a68-ff32-4e21-a5be-5d9d822e3a34","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015236","transactionId":"283342fb-4686-514f-a764-23e57ee93b8e","quoteStatus":"Draft","message":"Quote# Q-1015236 status changed to Draft.","modifiedAt":"2025-08-19T08:30:48.979Z"},"publishedAt":"2025-08-19T08:30:49.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:31:30
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:36]  Provided signature: sha256=432016daef4cb805e459deb17cacc114a0a4872af28f3fc10e110f6f6462b76f
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:37]  Calculated signature: sha256=432016daef4cb805e459deb17cacc114a0a4872af28f3fc10e110f6f6462b76f
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 105ab68788c3d3ee\n    [X-B3-Traceid] => 68a4365f5b2121e24109eb52794db8da\n    [B3] => 68a4365f5b2121e24109eb52794db8da-105ab68788c3d3ee-1\n    [Traceparent] => 00-68a4365f5b2121e24109eb52794db8da-105ab68788c3d3ee-01\n    [X-Amzn-Trace-Id] => Root=1-68a4365f-5b2121e24109eb52794db8da;Parent=105ab68788c3d3ee;Sampled=1\n    [X-Adsk-Signature] => sha256=432016daef4cb805e459deb17cacc114a0a4872af28f3fc10e110f6f6462b76f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1faffdf8-211c-45bc-8cd7-60c93cca80cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1faffdf8-211c-45bc-8cd7-60c93cca80cc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015239","transactionId":"2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7","quoteStatus":"Draft","message":"Quote# Q-1015239 status changed to Draft.","modifiedAt":"2025-08-19T08:31:27.894Z"},"publishedAt":"2025-08-19T08:31:28.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:31:30
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:36]  Provided signature: sha256=4047fc0bb2da854beb1a83d86ef4e09c11abc138699fb5baa4fe4a3a8fb2f9df
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:37]  Calculated signature: sha256=432016daef4cb805e459deb17cacc114a0a4872af28f3fc10e110f6f6462b76f
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d2bfa90cf6c91e64\n    [X-B3-Traceid] => 68a4365f5b2121e24109eb52794db8da\n    [B3] => 68a4365f5b2121e24109eb52794db8da-d2bfa90cf6c91e64-1\n    [Traceparent] => 00-68a4365f5b2121e24109eb52794db8da-d2bfa90cf6c91e64-01\n    [X-Amzn-Trace-Id] => Root=1-68a4365f-5b2121e24109eb52794db8da;Parent=d2bfa90cf6c91e64;Sampled=1\n    [X-Adsk-Signature] => sha256=4047fc0bb2da854beb1a83d86ef4e09c11abc138699fb5baa4fe4a3a8fb2f9df\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1faffdf8-211c-45bc-8cd7-60c93cca80cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:31:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1faffdf8-211c-45bc-8cd7-60c93cca80cc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015239","transactionId":"2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7","quoteStatus":"Draft","message":"Quote# Q-1015239 status changed to Draft.","modifiedAt":"2025-08-19T08:31:27.894Z"},"publishedAt":"2025-08-19T08:31:28.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:33:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:33:03
[webhook] [2025-08-19 08:33:03] [adwsapi_v2.php:36]  Provided signature: sha256=89458bee2713dd55a15f2f7114f7b63d2165af7cf36fbbbf50f5670eae07427e
[webhook] [2025-08-19 08:33:03] [adwsapi_v2.php:37]  Calculated signature: sha256=06d9deec0f4717b56f3fa3681d7b603daaea5c06e6035842132329d74da0f60b
[webhook] [2025-08-19 08:33:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cf147b53234b5522\n    [X-B3-Traceid] => 68a436bda839d8bce281437c3614acb9\n    [B3] => 68a436bda839d8bce281437c3614acb9-cf147b53234b5522-1\n    [Traceparent] => 00-68a436bda839d8bce281437c3614acb9-cf147b53234b5522-01\n    [X-Amzn-Trace-Id] => Root=1-68a436bd-a839d8bce281437c3614acb9;Parent=cf147b53234b5522;Sampled=1\n    [X-Adsk-Signature] => sha256=89458bee2713dd55a15f2f7114f7b63d2165af7cf36fbbbf50f5670eae07427e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 046e2e4c-32ea-4b45-abfe-efbfb3c6d31d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:33:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"046e2e4c-32ea-4b45-abfe-efbfb3c6d31d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015239","transactionId":"2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7","quoteStatus":"Quoted","message":"Quote# Q-1015239 status changed to Quoted.","modifiedAt":"2025-08-19T08:33:01.024Z"},"publishedAt":"2025-08-19T08:33:01.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:33:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:33:04
[webhook] [2025-08-19 08:33:04] [adwsapi_v2.php:36]  Provided signature: sha256=06d9deec0f4717b56f3fa3681d7b603daaea5c06e6035842132329d74da0f60b
[webhook] [2025-08-19 08:33:04] [adwsapi_v2.php:37]  Calculated signature: sha256=06d9deec0f4717b56f3fa3681d7b603daaea5c06e6035842132329d74da0f60b
[webhook] [2025-08-19 08:33:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 154e57474293da00\n    [X-B3-Traceid] => 68a436bda839d8bce281437c3614acb9\n    [B3] => 68a436bda839d8bce281437c3614acb9-154e57474293da00-1\n    [Traceparent] => 00-68a436bda839d8bce281437c3614acb9-154e57474293da00-01\n    [X-Amzn-Trace-Id] => Root=1-68a436bd-a839d8bce281437c3614acb9;Parent=154e57474293da00;Sampled=1\n    [X-Adsk-Signature] => sha256=06d9deec0f4717b56f3fa3681d7b603daaea5c06e6035842132329d74da0f60b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 046e2e4c-32ea-4b45-abfe-efbfb3c6d31d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:33:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"046e2e4c-32ea-4b45-abfe-efbfb3c6d31d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015239","transactionId":"2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7","quoteStatus":"Quoted","message":"Quote# Q-1015239 status changed to Quoted.","modifiedAt":"2025-08-19T08:33:01.024Z"},"publishedAt":"2025-08-19T08:33:01.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:34:28
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:36]  Provided signature: sha256=4f2bbee7db000d8d9c7da2657e1a5deb0f2c19ceba6d36ce7aa2af0590891e64
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:37]  Calculated signature: sha256=4f2bbee7db000d8d9c7da2657e1a5deb0f2c19ceba6d36ce7aa2af0590891e64
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 25a0704b41d5230b\n    [X-B3-Traceid] => 68a4371271d6f4377a14ae711841aae3\n    [B3] => 68a4371271d6f4377a14ae711841aae3-25a0704b41d5230b-1\n    [Traceparent] => 00-68a4371271d6f4377a14ae711841aae3-25a0704b41d5230b-01\n    [X-Amzn-Trace-Id] => Root=1-68a43712-71d6f4377a14ae711841aae3;Parent=25a0704b41d5230b;Sampled=1\n    [X-Adsk-Signature] => sha256=4f2bbee7db000d8d9c7da2657e1a5deb0f2c19ceba6d36ce7aa2af0590891e64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3c3fd46d-6431-4fd2-a1ec-e20008ea89cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"3c3fd46d-6431-4fd2-a1ec-e20008ea89cc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Order Submitted","message":"Quote# Q-1015216 status changed to Order Submitted.","modifiedAt":"2025-08-19T08:34:25.855Z"},"publishedAt":"2025-08-19T08:34:26.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:34:28
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:36]  Provided signature: sha256=531d48521e3c56ebe3c105b0feeba6fe99e435d94ec4a47aabf5df22409bf88b
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:37]  Calculated signature: sha256=4f2bbee7db000d8d9c7da2657e1a5deb0f2c19ceba6d36ce7aa2af0590891e64
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 381dfad0ec71a9be\n    [X-B3-Traceid] => 68a4371271d6f4377a14ae711841aae3\n    [B3] => 68a4371271d6f4377a14ae711841aae3-381dfad0ec71a9be-1\n    [Traceparent] => 00-68a4371271d6f4377a14ae711841aae3-381dfad0ec71a9be-01\n    [X-Amzn-Trace-Id] => Root=1-68a43712-71d6f4377a14ae711841aae3;Parent=381dfad0ec71a9be;Sampled=1\n    [X-Adsk-Signature] => sha256=531d48521e3c56ebe3c105b0feeba6fe99e435d94ec4a47aabf5df22409bf88b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3c3fd46d-6431-4fd2-a1ec-e20008ea89cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:34:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"3c3fd46d-6431-4fd2-a1ec-e20008ea89cc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Order Submitted","message":"Quote# Q-1015216 status changed to Order Submitted.","modifiedAt":"2025-08-19T08:34:25.855Z"},"publishedAt":"2025-08-19T08:34:26.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:34:29
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:36]  Provided signature: sha256=b9a3b9026edf30337177fa7305735d2f26375da0341507154e6427eaea103cc9
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:37]  Calculated signature: sha256=b9a3b9026edf30337177fa7305735d2f26375da0341507154e6427eaea103cc9
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4f96eded21fd4fd2\n    [X-B3-Traceid] => 68a437123132782a2a2fe3d0942bf6a2\n    [B3] => 68a437123132782a2a2fe3d0942bf6a2-4f96eded21fd4fd2-1\n    [Traceparent] => 00-68a437123132782a2a2fe3d0942bf6a2-4f96eded21fd4fd2-01\n    [X-Amzn-Trace-Id] => Root=1-68a43712-3132782a2a2fe3d0942bf6a2;Parent=4f96eded21fd4fd2;Sampled=1\n    [X-Adsk-Signature] => sha256=b9a3b9026edf30337177fa7305735d2f26375da0341507154e6427eaea103cc9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b2e18bac-02b6-491a-86a8-faf6c50e46c3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"b2e18bac-02b6-491a-86a8-faf6c50e46c3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Ordered","message":"Quote# Q-1015216 status changed to Ordered.","modifiedAt":"2025-08-19T08:34:26.787Z"},"publishedAt":"2025-08-19T08:34:27.000Z","csn":"5103159758"}
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 08:34:29
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:36]  Provided signature: sha256=5f0e3b0cb96593afbdea543ee316df02798675bc1e7452228b435b0725f20411
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:37]  Calculated signature: sha256=b9a3b9026edf30337177fa7305735d2f26375da0341507154e6427eaea103cc9
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1ef0289184cce28f\n    [X-B3-Traceid] => 68a437123132782a2a2fe3d0942bf6a2\n    [B3] => 68a437123132782a2a2fe3d0942bf6a2-1ef0289184cce28f-1\n    [Traceparent] => 00-68a437123132782a2a2fe3d0942bf6a2-1ef0289184cce28f-01\n    [X-Amzn-Trace-Id] => Root=1-68a43712-3132782a2a2fe3d0942bf6a2;Parent=1ef0289184cce28f;Sampled=1\n    [X-Adsk-Signature] => sha256=5f0e3b0cb96593afbdea543ee316df02798675bc1e7452228b435b0725f20411\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b2e18bac-02b6-491a-86a8-faf6c50e46c3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 08:34:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"b2e18bac-02b6-491a-86a8-faf6c50e46c3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1015216","transactionId":"9de4051a-c562-5e2f-84a6-6404f253fd52","quoteStatus":"Ordered","message":"Quote# Q-1015216 status changed to Ordered.","modifiedAt":"2025-08-19T08:34:26.787Z"},"publishedAt":"2025-08-19T08:34:27.000Z","csn":"5103159758"}
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 10:04:35
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:36]  Provided signature: sha256=85e3cddc3f5d2f2d8b9bbf6cbe3499fe04b11215d1cb9f1237de8afc1417f30f
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:37]  Calculated signature: sha256=85e3cddc3f5d2f2d8b9bbf6cbe3499fe04b11215d1cb9f1237de8afc1417f30f
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 24820ce28a108374\n    [X-B3-Traceid] => 68a44c31f5df97fb999d287b7dcf8565\n    [B3] => 68a44c31f5df97fb999d287b7dcf8565-24820ce28a108374-1\n    [Traceparent] => 00-68a44c31f5df97fb999d287b7dcf8565-24820ce28a108374-01\n    [X-Amzn-Trace-Id] => Root=1-68a44c31-f5df97fb999d287b7dcf8565;Parent=24820ce28a108374;Sampled=1\n    [X-Adsk-Signature] => sha256=85e3cddc3f5d2f2d8b9bbf6cbe3499fe04b11215d1cb9f1237de8afc1417f30f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c9655908-b476-4ae1-b086-17fe8fa4881f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"c9655908-b476-4ae1-b086-17fe8fa4881f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-992935","transactionId":"78671c3a-10ff-5a56-90cc-7d35c365807a","quoteStatus":"Ordered","message":"Quote# Q-992935 status changed to Ordered.","modifiedAt":"2025-08-19T10:04:33.310Z"},"publishedAt":"2025-08-19T10:04:33.000Z","csn":"5103159758"}
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 10:04:35
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:36]  Provided signature: sha256=f0ba4cbd1b561790cec4bcd84b4c93733eac5624a1eb39b92ac6a1b726ef14e5
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:37]  Calculated signature: sha256=85e3cddc3f5d2f2d8b9bbf6cbe3499fe04b11215d1cb9f1237de8afc1417f30f
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0b6edf80f10a2b71\n    [X-B3-Traceid] => 68a44c31f5df97fb999d287b7dcf8565\n    [B3] => 68a44c31f5df97fb999d287b7dcf8565-0b6edf80f10a2b71-1\n    [Traceparent] => 00-68a44c31f5df97fb999d287b7dcf8565-0b6edf80f10a2b71-01\n    [X-Amzn-Trace-Id] => Root=1-68a44c31-f5df97fb999d287b7dcf8565;Parent=0b6edf80f10a2b71;Sampled=1\n    [X-Adsk-Signature] => sha256=f0ba4cbd1b561790cec4bcd84b4c93733eac5624a1eb39b92ac6a1b726ef14e5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c9655908-b476-4ae1-b086-17fe8fa4881f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 10:04:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"c9655908-b476-4ae1-b086-17fe8fa4881f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-992935","transactionId":"78671c3a-10ff-5a56-90cc-7d35c365807a","quoteStatus":"Ordered","message":"Quote# Q-992935 status changed to Ordered.","modifiedAt":"2025-08-19T10:04:33.310Z"},"publishedAt":"2025-08-19T10:04:33.000Z","csn":"5103159758"}
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 10:07:19
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:36]  Provided signature: sha256=60b494ba571e7fb13565279de490f62969071b969a6bd4b65cb7b7bcf03faa69
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:37]  Calculated signature: sha256=60b494ba571e7fb13565279de490f62969071b969a6bd4b65cb7b7bcf03faa69
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f5d9dfed0c6fe3d6\n    [X-B3-Traceid] => 68a44cd5e02740040d2f00a8e84fac20\n    [B3] => 68a44cd5e02740040d2f00a8e84fac20-f5d9dfed0c6fe3d6-1\n    [Traceparent] => 00-68a44cd5e02740040d2f00a8e84fac20-f5d9dfed0c6fe3d6-01\n    [X-Amzn-Trace-Id] => Root=1-68a44cd5-e02740040d2f00a8e84fac20;Parent=f5d9dfed0c6fe3d6;Sampled=1\n    [X-Adsk-Signature] => sha256=60b494ba571e7fb13565279de490f62969071b969a6bd4b65cb7b7bcf03faa69\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 533ba59d-da02-4e09-852b-f82a7f802ab1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"533ba59d-da02-4e09-852b-f82a7f802ab1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-990570","transactionId":"bad6bd08-c710-54e2-ac20-dc066233b82a","quoteStatus":"Order Submitted","message":"Quote# Q-990570 status changed to Order Submitted.","modifiedAt":"2025-08-19T10:07:17.026Z"},"publishedAt":"2025-08-19T10:07:17.000Z","csn":"5103159758"}
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 10:07:19
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:36]  Provided signature: sha256=bf975dcf286eeef8db03ac02e9ea3e560b32793f9f492d5f6d8968c88d211115
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:37]  Calculated signature: sha256=60b494ba571e7fb13565279de490f62969071b969a6bd4b65cb7b7bcf03faa69
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d5d986616d69b182\n    [X-B3-Traceid] => 68a44cd5e02740040d2f00a8e84fac20\n    [B3] => 68a44cd5e02740040d2f00a8e84fac20-d5d986616d69b182-1\n    [Traceparent] => 00-68a44cd5e02740040d2f00a8e84fac20-d5d986616d69b182-01\n    [X-Amzn-Trace-Id] => Root=1-68a44cd5-e02740040d2f00a8e84fac20;Parent=d5d986616d69b182;Sampled=1\n    [X-Adsk-Signature] => sha256=bf975dcf286eeef8db03ac02e9ea3e560b32793f9f492d5f6d8968c88d211115\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 533ba59d-da02-4e09-852b-f82a7f802ab1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 10:07:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"533ba59d-da02-4e09-852b-f82a7f802ab1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-990570","transactionId":"bad6bd08-c710-54e2-ac20-dc066233b82a","quoteStatus":"Order Submitted","message":"Quote# Q-990570 status changed to Order Submitted.","modifiedAt":"2025-08-19T10:07:17.026Z"},"publishedAt":"2025-08-19T10:07:17.000Z","csn":"5103159758"}
[webhook] [2025-08-19 10:07:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 10:07:22
[webhook] [2025-08-19 10:07:22] [adwsapi_v2.php:36]  Provided signature: sha256=7857e2f490efbc4bafc083227c0ba5976ec95ffbff2165a5ef6941a6e871e673
[webhook] [2025-08-19 10:07:22] [adwsapi_v2.php:37]  Calculated signature: sha256=1df275e079f0a628a97b04f413c3125ac8ad8e18cfb8009383836858a686f42d
[webhook] [2025-08-19 10:07:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 40d6fde59b84aac7\n    [X-B3-Traceid] => 68a44cd8472279b755bc28d91d47b3e5\n    [B3] => 68a44cd8472279b755bc28d91d47b3e5-40d6fde59b84aac7-1\n    [Traceparent] => 00-68a44cd8472279b755bc28d91d47b3e5-40d6fde59b84aac7-01\n    [X-Amzn-Trace-Id] => Root=1-68a44cd8-472279b755bc28d91d47b3e5;Parent=40d6fde59b84aac7;Sampled=1\n    [X-Adsk-Signature] => sha256=7857e2f490efbc4bafc083227c0ba5976ec95ffbff2165a5ef6941a6e871e673\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 11ab2ad1-e875-4d9a-b240-a195b878a07c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-19 10:07:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"11ab2ad1-e875-4d9a-b240-a195b878a07c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-990570","transactionId":"bad6bd08-c710-54e2-ac20-dc066233b82a","quoteStatus":"Ordered","message":"Quote# Q-990570 status changed to Ordered.","modifiedAt":"2025-08-19T10:07:20.320Z"},"publishedAt":"2025-08-19T10:07:20.000Z","csn":"5103159758"}
[webhook] [2025-08-19 10:07:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-19 10:07:23
[webhook] [2025-08-19 10:07:23] [adwsapi_v2.php:36]  Provided signature: sha256=1df275e079f0a628a97b04f413c3125ac8ad8e18cfb8009383836858a686f42d
[webhook] [2025-08-19 10:07:23] [adwsapi_v2.php:37]  Calculated signature: sha256=1df275e079f0a628a97b04f413c3125ac8ad8e18cfb8009383836858a686f42d
[webhook] [2025-08-19 10:07:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 94c26e28e93077d4\n    [X-B3-Traceid] => 68a44cd8472279b755bc28d91d47b3e5\n    [B3] => 68a44cd8472279b755bc28d91d47b3e5-94c26e28e93077d4-1\n    [Traceparent] => 00-68a44cd8472279b755bc28d91d47b3e5-94c26e28e93077d4-01\n    [X-Amzn-Trace-Id] => Root=1-68a44cd8-472279b755bc28d91d47b3e5;Parent=94c26e28e93077d4;Sampled=1\n    [X-Adsk-Signature] => sha256=1df275e079f0a628a97b04f413c3125ac8ad8e18cfb8009383836858a686f42d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 11ab2ad1-e875-4d9a-b240-a195b878a07c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-19 10:07:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"11ab2ad1-e875-4d9a-b240-a195b878a07c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-990570","transactionId":"bad6bd08-c710-54e2-ac20-dc066233b82a","quoteStatus":"Ordered","message":"Quote# Q-990570 status changed to Ordered.","modifiedAt":"2025-08-19T10:07:20.320Z"},"publishedAt":"2025-08-19T10:07:20.000Z","csn":"5103159758"}
