[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-14 13:10:01] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-14 13:45:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-14 13:45:39] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-15 08:17:16] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-15 08:56:32] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-15 09:50:43] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-15 10:20:28] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-16 08:44:45] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-16 08:46:00] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:482] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:493] Using standard formatting for id
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:396] Generator debug - column: order_shipping_address, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_address
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_address
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:468] Analysis result for order_shipping_address: {"original_name":"order_shipping_address","suggested_name":"autobooks_order_shipping_address","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_address (confidence: 25%, suggested: autobooks_order_shipping_address)
[column_analyzer] [2025-08-16 11:54:09] [data_table_generator.class.php:493] Using standard formatting for order_shipping_address
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: order_shipping_address, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_address
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_address
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for order_shipping_address: {"original_name":"order_shipping_address","suggested_name":"autobooks_order_shipping_address","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_address (confidence: 25%, suggested: autobooks_order_shipping_address)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:493] Using standard formatting for order_shipping_address
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: order_shipping_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_city
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_city
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for order_shipping_city: {"original_name":"order_shipping_city","suggested_name":"order_shipping_city","confidence":10,"reasoning":"Confidence too low to suggest changes (score: 10)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":20,"pattern_matches":{"uk_locations":{"score":20,"matches":8,"total":20,"percentage":0.4,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":10}}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_city (confidence: 10%, suggested: order_shipping_city)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:493] Using standard formatting for order_shipping_city
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: order_shipping_state_province, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_state_province
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_state_province
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for order_shipping_state_province: {"original_name":"order_shipping_state_province","suggested_name":"autobooks_order_shipping_state_province","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":4,"total":4,"percentage":1,"type":"location"}},"sample_size":4},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_state_province (confidence: 25%, suggested: autobooks_order_shipping_state_province)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:493] Using standard formatting for order_shipping_state_province
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:396] Generator debug - column: order_shipping_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_country
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_country
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:468] Analysis result for order_shipping_country: {"original_name":"order_shipping_country","suggested_name":"autobooks_order_shipping_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_country (confidence: 25%, suggested: autobooks_order_shipping_country)
[column_analyzer] [2025-08-16 17:38:23] [data_table_generator.class.php:493] Using standard formatting for order_shipping_country
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: quantity, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: quantity
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: quantity
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for quantity: {"original_name":"quantity","suggested_name":"quantity","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:482] No intelligent naming applied for quantity (confidence: 0%, suggested: quantity)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:493] Using standard formatting for quantity
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: order_shipping_address, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_address
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_address
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for order_shipping_address: {"original_name":"order_shipping_address","suggested_name":"autobooks_order_shipping_address","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_address (confidence: 25%, suggested: autobooks_order_shipping_address)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:493] Using standard formatting for order_shipping_address
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:396] Generator debug - column: order_shipping_city, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_city
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_city
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:468] Analysis result for order_shipping_city: {"original_name":"order_shipping_city","suggested_name":"order_shipping_city","confidence":10,"reasoning":"Confidence too low to suggest changes (score: 10)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":20,"pattern_matches":{"uk_locations":{"score":20,"matches":8,"total":20,"percentage":0.4,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":10}}}
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_city (confidence: 10%, suggested: order_shipping_city)
[column_analyzer] [2025-08-16 18:38:20] [data_table_generator.class.php:493] Using standard formatting for order_shipping_city
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: quantity, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: quantity
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: quantity
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for quantity: {"original_name":"quantity","suggested_name":"quantity","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:482] No intelligent naming applied for quantity (confidence: 0%, suggested: quantity)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:493] Using standard formatting for quantity
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-17 19:57:24] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: quantity, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: quantity
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: quantity
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:468] Analysis result for quantity: {"original_name":"quantity","suggested_name":"quantity","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:482] No intelligent naming applied for quantity (confidence: 0%, suggested: quantity)
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:493] Using standard formatting for quantity
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-17 20:20:00] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:01] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:01] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-17 20:20:01] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:482] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:493] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:482] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:493] Using standard formatting for vendor_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:478] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:482] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:493] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:468] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-17 20:20:28] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:482] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:493] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:482] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:493] Using standard formatting for vendor_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:478] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:482] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:493] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:468] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-17 20:42:51] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: quantity, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: quantity
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: quantity
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for quantity: {"original_name":"quantity","suggested_name":"quantity","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:482] No intelligent naming applied for quantity (confidence: 0%, suggested: quantity)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:493] Using standard formatting for quantity
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:396] Generator debug - column: order_po_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_po_number
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_po_number
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:468] Analysis result for order_po_number: {"original_name":"order_po_number","suggested_name":"order_po_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:482] No intelligent naming applied for order_po_number (confidence: 0%, suggested: order_po_number)
[column_analyzer] [2025-08-17 21:17:57] [data_table_generator.class.php:493] Using standard formatting for order_po_number
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:482] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:493] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:482] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:493] Using standard formatting for vendor_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:478] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:482] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:493] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:468] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-17 21:25:07] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:482] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:493] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:482] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:493] Using standard formatting for vendor_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:478] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:482] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:493] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:468] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-17 22:40:24] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:482] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:493] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:482] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:493] Using standard formatting for vendor_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:478] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:482] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:493] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:468] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-17 22:43:42] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:482] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:493] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:482] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:493] Using standard formatting for vendor_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:478] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:482] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:493] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":31.875,"reasoning":"Medium confidence match for 'company_name' (score: 31.875)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":63.75,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":63.75,"matches":15,"total":20,"percentage":0.75,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":31.875}}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:478] Intelligent naming applied: end_customer_name -> company_name (confidence: 31.875)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:396] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:468] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:482] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-19 10:17:38] [data_table_generator.class.php:493] Using standard formatting for end_customer_address_2
