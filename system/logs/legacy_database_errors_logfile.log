[legacy_database_errors] [2025-08-16 11:44:35] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 11:44:35\n)\n
[legacy_database_errors] [2025-08-16 19:01:05] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 19:01:05\n)\n
[legacy_database_errors] [2025-08-16 19:01:06] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 19:01:06\n)\n
[legacy_database_errors] [2025-08-16 19:04:31] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 19:04:31\n)\n
[legacy_database_errors] [2025-08-16 19:15:39] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 19:15:39\n)\n
[legacy_database_errors] [2025-08-16 19:36:31] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 19:36:31\n)\n
[legacy_database_errors] [2025-08-16 19:54:32] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 19:54:32\n)\n
[legacy_database_errors] [2025-08-16 21:07:13] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 21:07:13\n)\n
[legacy_database_errors] [2025-08-16 21:09:56] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-16 21:09:56\n)\n
[legacy_database_errors] [2025-08-17 00:35:41] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-17 00:35:41\n)\n
[legacy_database_errors] [2025-08-17 19:04:55] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-17 19:04:55\n)\n
[legacy_database_errors] [2025-08-17 19:40:44] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-17 19:40:44\n)\n
[legacy_database_errors] [2025-08-17 19:42:23] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-17 19:42:23\n)\n
[legacy_database_errors] [2025-08-17 19:42:54] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-17 19:42:54\n)\n
[legacy_database_errors] [2025-08-17 19:44:16] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-17 19:44:16\n)\n
[legacy_database_errors] [2025-08-17 19:57:04] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-17 19:57:04\n)\n
[legacy_database_errors] [2025-08-18 14:47:54] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-18 14:47:54\n)\n
[legacy_database_errors] [2025-08-18 14:47:57] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-18 14:47:57\n)\n
[legacy_database_errors] [2025-08-18 15:36:34] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-18 15:36:34\n)\n
[legacy_database_errors] [2025-08-19 07:40:03] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-19 07:40:03\n)\n
[legacy_database_errors] [2025-08-19 08:56:30] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 7\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n    [ip_address] => *************\n    [timestamp] => 2025-08-19 08:56:30\n)\n
[legacy_database_errors] [2025-08-19 09:28:44] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-19 09:28:44\n)\n
[legacy_database_errors] [2025-08-19 09:53:53] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-19 09:53:53\n)\n
