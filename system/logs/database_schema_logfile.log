[database_schema] [2025-08-14 13:10:00] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VA<PERSON>HAR(255) NULL, `name` VA<PERSON>HAR(255) NULL, `contract` VA<PERSON>HAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-14 13:10:00\n)\n
[database_schema] [2025-08-14 13:45:37] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-14 13:45:37\n)\n
[database_schema] [2025-08-15 08:17:15] [database.class.php:1654] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-15 08:17:15\n)\n
[database_schema] [2025-08-15 08:56:31] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-15 08:56:31\n)\n
[database_schema] [2025-08-15 09:50:42] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-15 09:50:42\n)\n
[database_schema] [2025-08-15 10:20:28] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-15 10:20:28\n)\n
[database_schema] [2025-08-16 08:44:44] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-16 08:44:44\n)\n
[database_schema] [2025-08-16 08:45:59] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-16 08:45:59\n)\n
[database_schema] [2025-08-16 11:54:08] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-16 11:54:08\n)\n
[database_schema] [2025-08-16 17:38:22] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-16 17:38:22\n)\n
[database_schema] [2025-08-16 18:38:19] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-16 18:38:19\n)\n
[database_schema] [2025-08-17 19:57:23] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 19:57:23\n)\n
[database_schema] [2025-08-17 20:20:00] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 20:20:00\n)\n
[database_schema] [2025-08-17 20:20:27] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 20:20:27\n)\n
[database_schema] [2025-08-17 20:42:50] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 20:42:50\n)\n
[database_schema] [2025-08-17 21:17:56] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 21:17:56\n)\n
[database_schema] [2025-08-17 21:25:07] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 21:25:07\n)\n
[database_schema] [2025-08-17 22:40:24] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 22:40:24\n)\n
[database_schema] [2025-08-17 22:43:41] [database.class.php:1673] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-17 22:43:41\n)\n
[database_schema] [2025-08-19 10:17:37] [database.class.php:1675] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-19 10:17:37\n)\n
