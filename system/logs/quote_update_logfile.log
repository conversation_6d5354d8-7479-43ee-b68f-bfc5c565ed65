[quote_update] [2025-08-14 15:41:38] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:41:38
[quote_update] [2025-08-14 15:41:38] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:41:38] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 687f4d53-4da9-4337-82ff-e93e223e729c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007484\n            [transactionId] => 76a084a2-65fc-57f3-a115-e5ba601e7437\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1007484 status changed to Draft.\n            [modifiedAt] => 2025-08-14T15:41:35.610Z\n        )\n\n    [publishedAt] => 2025-08-14T15:41:36.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:41:38] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:41:38
[quote_update] [2025-08-14 15:41:38] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:41:38] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 687f4d53-4da9-4337-82ff-e93e223e729c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007484\n            [transactionId] => 76a084a2-65fc-57f3-a115-e5ba601e7437\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1007484 status changed to Draft.\n            [modifiedAt] => 2025-08-14T15:41:35.610Z\n        )\n\n    [publishedAt] => 2025-08-14T15:41:36.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:41:40] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 14 Aug 2025 15:41:40 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3950\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d0666ccc-1544-48e7-a6ae-35928729e2a2\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PTWEJFgqIAMEnZA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689e03b3-0988037921c8e790389b2755\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1007484\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-14T16:41:33+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 61.5\n                            [totalNetAmount] => 1168.5\n                            [estimatedTax] => 233.7\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1402.2\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Stefan Martin\n                            [addressLine1] => 26 Woodcote Avenue\n                            [city] => Nuneaton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CV11 6DE\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Stefan\n                            [lastName] => Martin\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-04\n                                    [endDate] => 2028-10-03\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 66488057033384\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-03\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1230\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 5\n                                            [renewalDiscountAmount] => 61.5\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 61.5\n                                            [extendedDiscountedSRP] => 1168.5\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1168.5\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-14\n                                                            [startDate] => 2025-10-04\n                                                            [endDate] => 2026-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [1] => Array\n                                                        (\n                                                            [plan] => 2\n                                                            [billDate] => 2026-10-04\n                                                            [startDate] => 2026-10-04\n                                                            [endDate] => 2027-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [2] => Array\n                                                        (\n                                                            [plan] => 3\n                                                            [billDate] => 2027-10-04\n                                                            [startDate] => 2027-10-04\n                                                            [endDate] => 2028-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1007484\n)\n
[quote_update] [2025-08-14 15:41:40] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 14 Aug 2025 15:41:40 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3950\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 16f95ab6-0544-4769-8ce0-b9bb3706be14\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PTWEJGxFoAMEahQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689e03b3-7290aa2e422e38d11247cd0b\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1007484\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-14T16:41:33+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 61.5\n                            [totalNetAmount] => 1168.5\n                            [estimatedTax] => 233.7\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1402.2\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Stefan Martin\n                            [addressLine1] => 26 Woodcote Avenue\n                            [city] => Nuneaton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CV11 6DE\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Stefan\n                            [lastName] => Martin\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-04\n                                    [endDate] => 2028-10-03\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 66488057033384\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-03\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1230\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 5\n                                            [renewalDiscountAmount] => 61.5\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 61.5\n                                            [extendedDiscountedSRP] => 1168.5\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1168.5\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-14\n                                                            [startDate] => 2025-10-04\n                                                            [endDate] => 2026-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [1] => Array\n                                                        (\n                                                            [plan] => 2\n                                                            [billDate] => 2026-10-04\n                                                            [startDate] => 2026-10-04\n                                                            [endDate] => 2027-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [2] => Array\n                                                        (\n                                                            [plan] => 3\n                                                            [billDate] => 2027-10-04\n                                                            [startDate] => 2027-10-04\n                                                            [endDate] => 2028-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1007484\n)\n
[quote_update] [2025-08-14 15:41:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:41:42
[quote_update] [2025-08-14 15:41:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:41:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2fdb127c-3fde-4f21-9466-2036b964fee5\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007485\n            [transactionId] => fbf37246-eb4a-5e2d-bee6-135eeea00b12\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1007485 status changed to Draft.\n            [modifiedAt] => 2025-08-14T15:41:37.439Z\n        )\n\n    [publishedAt] => 2025-08-14T15:41:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:41:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:41:42
[quote_update] [2025-08-14 15:41:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:41:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2fdb127c-3fde-4f21-9466-2036b964fee5\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007485\n            [transactionId] => fbf37246-eb4a-5e2d-bee6-135eeea00b12\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1007485 status changed to Draft.\n            [modifiedAt] => 2025-08-14T15:41:37.439Z\n        )\n\n    [publishedAt] => 2025-08-14T15:41:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:41:43] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 14 Aug 2025 15:41:43 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3950\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 15a97303-520a-4e75-8218-baeebb25310c\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PTWEqG7jIAMEk4Q=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689e03b7-370adea3770c5b6c0b812847\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1007485\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-14T16:41:35+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 61.5\n                            [totalNetAmount] => 1168.5\n                            [estimatedTax] => 233.7\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1402.2\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Stefan Martin\n                            [addressLine1] => 26 Woodcote Avenue\n                            [city] => Nuneaton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CV11 6DE\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Stefan\n                            [lastName] => Martin\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-04\n                                    [endDate] => 2028-10-03\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 66488057033384\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-03\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1230\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 5\n                                            [renewalDiscountAmount] => 61.5\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 61.5\n                                            [extendedDiscountedSRP] => 1168.5\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1168.5\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-14\n                                                            [startDate] => 2025-10-04\n                                                            [endDate] => 2026-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [1] => Array\n                                                        (\n                                                            [plan] => 2\n                                                            [billDate] => 2026-10-04\n                                                            [startDate] => 2026-10-04\n                                                            [endDate] => 2027-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [2] => Array\n                                                        (\n                                                            [plan] => 3\n                                                            [billDate] => 2027-10-04\n                                                            [startDate] => 2027-10-04\n                                                            [endDate] => 2028-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1007485\n)\n
[quote_update] [2025-08-14 15:41:44] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 14 Aug 2025 15:41:44 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3950\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 66897a5c-c7c7-465b-8cc6-99ccba534496\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PTWErFIGoAMEY_w=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689e03b7-39fa1a0c6ca15a1c175f0bd8\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1007485\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-14T16:41:35+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 61.5\n                            [totalNetAmount] => 1168.5\n                            [estimatedTax] => 233.7\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1402.2\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Stefan Martin\n                            [addressLine1] => 26 Woodcote Avenue\n                            [city] => Nuneaton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CV11 6DE\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Stefan\n                            [lastName] => Martin\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-04\n                                    [endDate] => 2028-10-03\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 66488057033384\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-03\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1230\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 5\n                                            [renewalDiscountAmount] => 61.5\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 61.5\n                                            [extendedDiscountedSRP] => 1168.5\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1168.5\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-14\n                                                            [startDate] => 2025-10-04\n                                                            [endDate] => 2026-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [1] => Array\n                                                        (\n                                                            [plan] => 2\n                                                            [billDate] => 2026-10-04\n                                                            [startDate] => 2026-10-04\n                                                            [endDate] => 2027-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [2] => Array\n                                                        (\n                                                            [plan] => 3\n                                                            [billDate] => 2027-10-04\n                                                            [startDate] => 2027-10-04\n                                                            [endDate] => 2028-10-03\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1007485\n)\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:42:14
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 81ace513-75c2-4c4f-ac1b-f77c4b77bc27\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007484\n            [transactionId] => 76a084a2-65fc-57f3-a115-e5ba601e7437\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1007484 status changed to Quoted.\n            [modifiedAt] => 2025-08-14T15:42:11.166Z\n        )\n\n    [publishedAt] => 2025-08-14T15:42:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:42:14
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 81ace513-75c2-4c4f-ac1b-f77c4b77bc27\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007484\n            [transactionId] => 76a084a2-65fc-57f3-a115-e5ba601e7437\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1007484 status changed to Quoted.\n            [modifiedAt] => 2025-08-14T15:42:11.166Z\n        )\n\n    [publishedAt] => 2025-08-14T15:42:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-14 15:42:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1007484', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007484', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:42:34
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 896e2739-d970-4be5-aa84-a0b6a339189b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007485\n            [transactionId] => fbf37246-eb4a-5e2d-bee6-135eeea00b12\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1007485 status changed to Quoted.\n            [modifiedAt] => 2025-08-14T15:42:31.776Z\n        )\n\n    [publishedAt] => 2025-08-14T15:42:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-14 15:42:34] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-14 15:42:35
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 896e2739-d970-4be5-aa84-a0b6a339189b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1007485\n            [transactionId] => fbf37246-eb4a-5e2d-bee6-135eeea00b12\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1007485 status changed to Quoted.\n            [modifiedAt] => 2025-08-14T15:42:31.776Z\n        )\n\n    [publishedAt] => 2025-08-14T15:42:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-14 15:42:35] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1007485', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1007485', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 08:51:01] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 08:51:01
[quote_update] [2025-08-15 08:51:01] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 08:51:01] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9e41cc9d-3b4b-4005-b8cd-395be2eda5a4\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T08:50:59.055Z\n        )\n\n    [publishedAt] => 2025-08-15T08:50:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 08:51:01] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 08:51:01
[quote_update] [2025-08-15 08:51:01] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 08:51:01] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9e41cc9d-3b4b-4005-b8cd-395be2eda5a4\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T08:50:59.055Z\n        )\n\n    [publishedAt] => 2025-08-15T08:50:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 08:51:03] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 08:51:03 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1596\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 933aa5c8-db2a-4af2-af65-4581bb6f6dff\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PVs2pEhQoAMEjEQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689ef4f6-0a7263ea2efb9e0e4f32169c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T09:50:55+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Lester Aldridge\n                            [addressLine1] => Mountbatten House Grosvenor Square\n                            [city] => Southampton\n                            [stateProvinceCode] => \n                            [stateProvince] => HAMPSHIRE\n                            [postalCode] => SO15 2JU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => estelle\n                            [lastName] => boullet\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 08:51:03] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 08:51:03 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1596\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d7576525-5bb4-4ef7-86b6-1c103f65e329\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PVs2pEZaoAMEngw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689ef4f6-2ef6124e204174622857d138\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T09:50:55+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Lester Aldridge\n                            [addressLine1] => Mountbatten House Grosvenor Square\n                            [city] => Southampton\n                            [stateProvinceCode] => \n                            [stateProvince] => HAMPSHIRE\n                            [postalCode] => SO15 2JU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => estelle\n                            [lastName] => boullet\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 08:51:54
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ed6adefd-8ec8-498a-ae03-0dd3b9563f3d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T08:51:51.933Z\n        )\n\n    [publishedAt] => 2025-08-15T08:51:52.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 08:51:54
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ed6adefd-8ec8-498a-ae03-0dd3b9563f3d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T08:51:51.933Z\n        )\n\n    [publishedAt] => 2025-08-15T08:51:52.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 08:51:54] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 10:21:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:21:08
[quote_update] [2025-08-15 10:21:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:21:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => bd4c0361-afaa-48d2-8cbe-27a0d95a026d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1008728\n            [transactionId] => 35a8b854-ddc9-5b45-9e5b-d94dd1f75d44\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1008728 status changed to Draft.\n            [modifiedAt] => 2025-08-15T10:21:05.814Z\n        )\n\n    [publishedAt] => 2025-08-15T10:21:06.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:21:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:21:08
[quote_update] [2025-08-15 10:21:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:21:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => bd4c0361-afaa-48d2-8cbe-27a0d95a026d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1008728\n            [transactionId] => 35a8b854-ddc9-5b45-9e5b-d94dd1f75d44\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1008728 status changed to Draft.\n            [modifiedAt] => 2025-08-15T10:21:05.814Z\n        )\n\n    [publishedAt] => 2025-08-15T10:21:06.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:21:10] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 10:21:10 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3936\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 45e712f0-f878-430c-976f-a37285318a3d\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PV6DfF9CIAMEQtA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f0a15-372080772633cefc23ff94f7\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1008728\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T11:21:03+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 61.5\n                            [totalNetAmount] => 1168.5\n                            [estimatedTax] => 233.7\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1402.2\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => 1\n                            [name] => Davies Mr\n                            [addressLine1] => 43 Stroud Road\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => LONDON\n                            [postalCode] => SW19 8DQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => colin\n                            [lastName] => davies\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-25\n                                    [endDate] => 2028-10-24\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55543580065940\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-24\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1230\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 5\n                                            [renewalDiscountAmount] => 61.5\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 61.5\n                                            [extendedDiscountedSRP] => 1168.5\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1168.5\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-10-25\n                                                            [endDate] => 2026-10-24\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [1] => Array\n                                                        (\n                                                            [plan] => 2\n                                                            [billDate] => 2026-10-25\n                                                            [startDate] => 2026-10-25\n                                                            [endDate] => 2027-10-24\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [2] => Array\n                                                        (\n                                                            [plan] => 3\n                                                            [billDate] => 2027-10-25\n                                                            [startDate] => 2027-10-25\n                                                            [endDate] => 2028-10-24\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1008728\n)\n
[quote_update] [2025-08-15 10:21:11] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 10:21:11 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3936\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 3328a19e-67cb-4bc9-bdc8-5dab1a161431\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PV6DfH94oAMEMOg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f0a15-5e49705e5b9061166872ced7\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1008728\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T11:21:03+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 61.5\n                            [totalNetAmount] => 1168.5\n                            [estimatedTax] => 233.7\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1402.2\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => 1\n                            [name] => Davies Mr\n                            [addressLine1] => 43 Stroud Road\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => LONDON\n                            [postalCode] => SW19 8DQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => colin\n                            [lastName] => davies\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-25\n                                    [endDate] => 2028-10-24\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55543580065940\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-24\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1230\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 5\n                                            [renewalDiscountAmount] => 61.5\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 61.5\n                                            [extendedDiscountedSRP] => 1168.5\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1168.5\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-10-25\n                                                            [endDate] => 2026-10-24\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [1] => Array\n                                                        (\n                                                            [plan] => 2\n                                                            [billDate] => 2026-10-25\n                                                            [startDate] => 2026-10-25\n                                                            [endDate] => 2027-10-24\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                    [2] => Array\n                                                        (\n                                                            [plan] => 3\n                                                            [billDate] => 2027-10-25\n                                                            [startDate] => 2027-10-25\n                                                            [endDate] => 2028-10-24\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 20.5\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 389.5\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A06\n                                                    [description] => 3 year\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1008728\n)\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:21:36
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 0d1be9fc-c8f6-43eb-ad27-f74e519c585f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1008728\n            [transactionId] => 35a8b854-ddc9-5b45-9e5b-d94dd1f75d44\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1008728 status changed to Quoted.\n            [modifiedAt] => 2025-08-15T10:21:33.018Z\n        )\n\n    [publishedAt] => 2025-08-15T10:21:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:21:36
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 0d1be9fc-c8f6-43eb-ad27-f74e519c585f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1008728\n            [transactionId] => 35a8b854-ddc9-5b45-9e5b-d94dd1f75d44\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1008728 status changed to Quoted.\n            [modifiedAt] => 2025-08-15T10:21:33.018Z\n        )\n\n    [publishedAt] => 2025-08-15T10:21:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 10:21:36] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1008728', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1008728', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:31:59
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 26e812fd-86ae-489c-ba83-58b96eef1f1b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-988001\n            [transactionId] => 6b16c5d9-1a00-52bc-ad64-a4d468d3b383\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-988001 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-15T10:31:56.032Z\n        )\n\n    [publishedAt] => 2025-08-15T10:31:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:31:59
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 26e812fd-86ae-489c-ba83-58b96eef1f1b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-988001\n            [transactionId] => 6b16c5d9-1a00-52bc-ad64-a4d468d3b383\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-988001 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-15T10:31:56.032Z\n        )\n\n    [publishedAt] => 2025-08-15T10:31:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 10:31:59] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:32:12
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 5fb1136f-8abc-485b-bbd9-755c81cd334c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-988001\n            [transactionId] => 6b16c5d9-1a00-52bc-ad64-a4d468d3b383\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-988001 status changed to Ordered.\n            [modifiedAt] => 2025-08-15T10:32:09.774Z\n        )\n\n    [publishedAt] => 2025-08-15T10:32:10.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 10:32:12
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 5fb1136f-8abc-485b-bbd9-755c81cd334c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-988001\n            [transactionId] => 6b16c5d9-1a00-52bc-ad64-a4d468d3b383\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-988001 status changed to Ordered.\n            [modifiedAt] => 2025-08-15T10:32:09.774Z\n        )\n\n    [publishedAt] => 2025-08-15T10:32:10.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 10:32:12] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-988001', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-988001', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 12:13:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:13:06
[quote_update] [2025-08-15 12:13:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:13:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 256eaf91-5492-4be9-ab30-b876b1a82a7a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 53e5c4bb-7c5a-5d16-8101-a5a25ad056c4\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:13:03.357Z\n        )\n\n    [publishedAt] => 2025-08-15T12:13:03.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:13:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:13:06
[quote_update] [2025-08-15 12:13:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:13:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 256eaf91-5492-4be9-ab30-b876b1a82a7a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 53e5c4bb-7c5a-5d16-8101-a5a25ad056c4\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:13:03.357Z\n        )\n\n    [publishedAt] => 2025-08-15T12:13:03.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:13:07] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:13:07 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 04229d89-8046-40ba-81c5-17ef86e24c3e\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWKdEE6MIAMEs9A=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2453-1694867a0ac1112f317b1e0f\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:13:01+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:13:08] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:13:08 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => e00964dd-20eb-4af2-b61e-4244e8785539\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWKdGH1zoAMElgQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2453-73ff44804eb19db710c6253f\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:13:01+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:15:15] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:15:15
[quote_update] [2025-08-15 12:15:15] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:15:15] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => df6a7968-6a24-40c9-82e5-00f3b24f3263\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => a1d45fd4-4607-5cc4-a27d-3ecd610789bc\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:15:12.933Z\n        )\n\n    [publishedAt] => 2025-08-15T12:15:13.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:15:15] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:15:15
[quote_update] [2025-08-15 12:15:15] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:15:15] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => df6a7968-6a24-40c9-82e5-00f3b24f3263\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => a1d45fd4-4607-5cc4-a27d-3ecd610789bc\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:15:12.933Z\n        )\n\n    [publishedAt] => 2025-08-15T12:15:13.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:15:17] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:15:17 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 3e240313-8f2e-46a2-b21c-2a7273ebabd3\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWKxUEJQIAMEF1w=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f24d4-6074223a5d22b12916c7e6b9\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:15:11+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:15:17] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:15:17 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 8defc219-8708-4b6a-b338-c9d72dc0b1bc\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWKxUE5fIAMEDgw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f24d4-7da03d0f1c57312a10def103\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:15:11+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:32:44] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:32:44
[quote_update] [2025-08-15 12:32:44] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:32:44] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f030315e-cf82-4958-909f-0be7c762e595\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 0df1ca79-dfea-5df3-b791-ab60f4f52469\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:32:42.025Z\n        )\n\n    [publishedAt] => 2025-08-15T12:32:42.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:32:45] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:32:45
[quote_update] [2025-08-15 12:32:45] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:32:45] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f030315e-cf82-4958-909f-0be7c762e595\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 0df1ca79-dfea-5df3-b791-ab60f4f52469\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:32:42.025Z\n        )\n\n    [publishedAt] => 2025-08-15T12:32:42.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:32:46] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:32:46 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1562\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => ad033f6d-18ac-4b29-901a-87eb1603996c\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWNVOGrCoAMEnZA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f28ed-08d168283f769f7764152aea\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:32:39+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Xtinguish Ltd\n                            [addressLine1] => 46 Conyngham Road\n                            [city] => Northampton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Patel\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:32:47] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:32:46 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1562\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 0ada9dcb-38e3-4f9d-b47f-42f559ef07c4\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWNVPF81oAMEHmQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f28ed-7dc07ee50ace657a0feee18e\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:32:39+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Xtinguish Ltd\n                            [addressLine1] => 46 Conyngham Road\n                            [city] => Northampton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Patel\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:34:54
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 5320ed6e-abbd-4e55-8e41-3d27e93e6aca\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 0df1ca79-dfea-5df3-b791-ab60f4f52469\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:34:51.777Z\n        )\n\n    [publishedAt] => 2025-08-15T12:34:52.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 12:34:54] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:34:55
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 5320ed6e-abbd-4e55-8e41-3d27e93e6aca\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 0df1ca79-dfea-5df3-b791-ab60f4f52469\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:34:51.777Z\n        )\n\n    [publishedAt] => 2025-08-15T12:34:52.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 12:34:55] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 12:35:32] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:35:32
[quote_update] [2025-08-15 12:35:32] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:35:32] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 6e89d406-dd58-425f-9a83-d4c2483f123e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1008866\n            [transactionId] => 09e57328-ce64-558e-97bb-1231e5a2e86a\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1008866 status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:35:29.276Z\n        )\n\n    [publishedAt] => 2025-08-15T12:35:29.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:35:32] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:35:32
[quote_update] [2025-08-15 12:35:32] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:35:32] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 6e89d406-dd58-425f-9a83-d4c2483f123e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1008866\n            [transactionId] => 09e57328-ce64-558e-97bb-1231e5a2e86a\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1008866 status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:35:29.276Z\n        )\n\n    [publishedAt] => 2025-08-15T12:35:29.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:35:33] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:35:33 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 5429\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => be6f02a3-0159-4cc1-88af-a53d6ec9c1c0\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWNvYHCSoAMEfAg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2995-6df5d92b61543db31f93e2a1\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1008866\n                    [pdfLink] => \n                    [opportunityNumber] => A-32552883\n                    [quoteCreatedTime] => 2025-08-15T13:35:27+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 3715\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 3715\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 3715\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Xtinguish Ltd\n                            [addressLine1] => 46 Conyngham Road\n                            [city] => Northampton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Patel\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => \n                                    [endDate] => \n                                    [offeringId] => OD-000052\n                                    [offeringCode] => AECCOL\n                                    [offeringName] => AEC Collection\n                                    [marketingName] => Architecture Engineering & Construction Collection\n                                    [action] => New\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => \n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 2850\n                                            [extendedSRP] => 2850\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 2850\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 2850\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 2850\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 2850\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                            [1] => Array\n                                (\n                                    [lineNumber] => 2\n                                    [quoteLineNumber] => QL-1954694\n                                    [startDate] => \n                                    [endDate] => \n                                    [offeringId] => OD-000125\n                                    [offeringCode] => COLLRP\n                                    [offeringName] => BIM Collaborate Pro\n                                    [marketingName] => BIM Collaborate Pro\n                                    [action] => New\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => \n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 865\n                                            [extendedSRP] => 865\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 865\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 865\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 865\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 865\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1008866\n)\n
[quote_update] [2025-08-15 12:35:33] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:35:33 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 5429\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 1719acec-be06-4b08-825c-c43771120bd9\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWNvYGMkoAMEsYQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2995-6a7e7d7b7bdcf2eb1c573943\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1008866\n                    [pdfLink] => \n                    [opportunityNumber] => A-32552883\n                    [quoteCreatedTime] => 2025-08-15T13:35:27+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 3715\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 3715\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 3715\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Xtinguish Ltd\n                            [addressLine1] => 46 Conyngham Road\n                            [city] => Northampton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Patel\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => \n                                    [endDate] => \n                                    [offeringId] => OD-000052\n                                    [offeringCode] => AECCOL\n                                    [offeringName] => AEC Collection\n                                    [marketingName] => Architecture Engineering & Construction Collection\n                                    [action] => New\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => \n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 2850\n                                            [extendedSRP] => 2850\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 2850\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 2850\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 2850\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 2850\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                            [1] => Array\n                                (\n                                    [lineNumber] => 2\n                                    [quoteLineNumber] => QL-1954694\n                                    [startDate] => \n                                    [endDate] => \n                                    [offeringId] => OD-000125\n                                    [offeringCode] => COLLRP\n                                    [offeringName] => BIM Collaborate Pro\n                                    [marketingName] => BIM Collaborate Pro\n                                    [action] => New\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => \n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 865\n                                            [extendedSRP] => 865\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 865\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 865\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 865\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 865\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1008866\n)\n
[quote_update] [2025-08-15 12:37:21] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:37:21
[quote_update] [2025-08-15 12:37:21] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:37:21] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 811a2d50-ff1b-4016-aed4-161cf1a93574\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:37:18.766Z\n        )\n\n    [publishedAt] => 2025-08-15T12:37:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:37:21] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:37:21
[quote_update] [2025-08-15 12:37:21] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:37:21] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 811a2d50-ff1b-4016-aed4-161cf1a93574\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:37:18.766Z\n        )\n\n    [publishedAt] => 2025-08-15T12:37:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:37:23] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:37:23 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1562\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => c126b2cc-7e68-44d7-b089-c873f845bade\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWOAdHpJoAMEe1g=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2a02-649934d470ec81d522f9e797\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:37:16+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Xtinguish Ltd\n                            [addressLine1] => 46 Conyngham Road\n                            [city] => Northampton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Patel\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:37:23] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:37:23 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1562\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d85e5ba2-2f8c-4ca6-a8b0-cfef8236f563\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWOAdGN-IAMEjZA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2a02-55a4557c6e36c0dc7a1b5e41\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:37:16+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Xtinguish Ltd\n                            [addressLine1] => 46 Conyngham Road\n                            [city] => Northampton\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Patel\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:38:42
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1c5dceb1-45db-409f-b9d1-d4364496c5e5\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:38:39.476Z\n        )\n\n    [publishedAt] => 2025-08-15T12:38:40.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:38:42
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1c5dceb1-45db-409f-b9d1-d4364496c5e5\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:38:39.476Z\n        )\n\n    [publishedAt] => 2025-08-15T12:38:40.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 12:38:42] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 12:39:41] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:39:41
[quote_update] [2025-08-15 12:39:41] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:39:41] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => b2a9c09c-364d-4eed-86fc-348fce473c64\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d64fcc5c-9474-536f-a58b-ad45b8bcf165\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:39:38.040Z\n        )\n\n    [publishedAt] => 2025-08-15T12:39:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:39:41] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:39:41
[quote_update] [2025-08-15 12:39:41] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:39:41] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => b2a9c09c-364d-4eed-86fc-348fce473c64\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d64fcc5c-9474-536f-a58b-ad45b8bcf165\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:39:38.040Z\n        )\n\n    [publishedAt] => 2025-08-15T12:39:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:39:42] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:39:42 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 3552e08a-b5b7-46d3-98e2-ebd0fea1256c\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWOWQHWWIAMEbYg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2a8e-662795dd5a4d1fbf74bfe93c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:39:36+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:39:42] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:39:42 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => bab641fd-40b6-45a1-a5b6-fac783f0f443\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWOWQHTZIAMEixQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2a8e-1354822e3002ac1930ae09a2\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:39:36+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:40:50
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 06638a2c-4457-4d4c-b603-d35dd6dccc1f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d64fcc5c-9474-536f-a58b-ad45b8bcf165\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:40:48.020Z\n        )\n\n    [publishedAt] => 2025-08-15T12:40:48.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:40:50
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 06638a2c-4457-4d4c-b603-d35dd6dccc1f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d64fcc5c-9474-536f-a58b-ad45b8bcf165\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:40:48.020Z\n        )\n\n    [publishedAt] => 2025-08-15T12:40:48.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 12:40:50] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 12:41:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:41:04
[quote_update] [2025-08-15 12:41:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:41:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c304d439-8ac3-4e2c-94a1-6b0f6a3325a6\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => f1e6afc8-2ad9-551d-96fa-ced107c8ceda\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:41:02.015Z\n        )\n\n    [publishedAt] => 2025-08-15T12:41:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:41:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:41:04
[quote_update] [2025-08-15 12:41:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:41:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c304d439-8ac3-4e2c-94a1-6b0f6a3325a6\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => f1e6afc8-2ad9-551d-96fa-ced107c8ceda\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-15T12:41:02.015Z\n        )\n\n    [publishedAt] => 2025-08-15T12:41:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:41:06] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:41:06 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 319b520b-3ef0-4e32-b4d2-30e5db503225\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWOjSFOqoAMEfgA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2ae1-2f7a402f0ffa552934045923\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:41:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:41:06] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 12:41:06 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1642\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => b9c515b6-1277-4bf6-a6f0-3a6b50967214\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWOjTGSroAMEPMw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f2ae1-3c344cff3de05de97eb22346\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T13:41:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Reform Architecture and Interior De\n                            [addressLine1] => Unit 6 The Garage Studios 41-45 St.\n                            [addressLine2] => Marys Gate\n                            [city] => Nottingham\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG1 1PU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jamie\n                            [lastName] => King\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:42:14
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 847613d9-57cc-4e12-b6bb-259376922099\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => f1e6afc8-2ad9-551d-96fa-ced107c8ceda\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:42:11.714Z\n        )\n\n    [publishedAt] => 2025-08-15T12:42:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 12:42:14
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 847613d9-57cc-4e12-b6bb-259376922099\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => f1e6afc8-2ad9-551d-96fa-ced107c8ceda\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-15T12:42:11.714Z\n        )\n\n    [publishedAt] => 2025-08-15T12:42:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 12:42:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:02:02
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:02:02
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ce459fe4-91a4-41d7-8fe0-74330aa547e2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 0df1ca79-dfea-5df3-b791-ab60f4f52469\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-15T13:01:59.305Z\n        )\n\n    [publishedAt] => 2025-08-15T13:01:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ce459fe4-91a4-41d7-8fe0-74330aa547e2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 0df1ca79-dfea-5df3-b791-ab60f4f52469\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-15T13:01:59.305Z\n        )\n\n    [publishedAt] => 2025-08-15T13:01:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 13:02:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:03:48
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1919c8ac-cf2a-4b3c-86b6-fa7aa540727e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Order Submitted\n            [message] => Quote# ********* status changed to Order Submitted.\n            [modifiedAt] => 2025-08-15T13:03:45.269Z\n        )\n\n    [publishedAt] => 2025-08-15T13:03:45.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:03:48
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1919c8ac-cf2a-4b3c-86b6-fa7aa540727e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Order Submitted\n            [message] => Quote# ********* status changed to Order Submitted.\n            [modifiedAt] => 2025-08-15T13:03:45.269Z\n        )\n\n    [publishedAt] => 2025-08-15T13:03:45.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 13:03:48] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:03:49
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => b78485a9-8105-424e-9094-e85150e7fae0\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-15T13:03:46.752Z\n        )\n\n    [publishedAt] => 2025-08-15T13:03:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:03:49
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => b78485a9-8105-424e-9094-e85150e7fae0\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 8df5af2b-a328-5a0b-80c9-ce328f1bb774\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-15T13:03:46.752Z\n        )\n\n    [publishedAt] => 2025-08-15T13:03:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 13:03:49] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 13:56:44] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:56:44
[quote_update] [2025-08-15 13:56:44] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:56:44] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 50529b1c-8a52-4ece-ad3f-86af2c6b65a2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1009008\n            [transactionId] => f0e009d3-7c9d-57ca-ac76-68ca4253d1e2\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1009008 status changed to Draft.\n            [modifiedAt] => 2025-08-15T13:56:42.181Z\n        )\n\n    [publishedAt] => 2025-08-15T13:56:42.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:56:44] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:56:44
[quote_update] [2025-08-15 13:56:44] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:56:44] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 50529b1c-8a52-4ece-ad3f-86af2c6b65a2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1009008\n            [transactionId] => f0e009d3-7c9d-57ca-ac76-68ca4253d1e2\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1009008 status changed to Draft.\n            [modifiedAt] => 2025-08-15T13:56:42.181Z\n        )\n\n    [publishedAt] => 2025-08-15T13:56:42.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:56:46] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 13:56:46 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3585\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d7ed9549-8144-4776-93b7-80ff0c87144f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWZouEO0IAMEokQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f3c9d-3572978364bdde926296532f\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1009008\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T14:56:39+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => MICHAEL VAUGHAN RACKING SERVIC\n                            [addressLine1] => 3 Gibbs Close\n                            [city] => Swindon\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => SN3 5EW\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Michael\n                            [lastName] => Vaughan\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-16\n                                    [endDate] => 2026-09-15\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56862671431857\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-15\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-09-16\n                                                            [endDate] => 2026-09-15\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1009008\n)\n
[quote_update] [2025-08-15 13:56:46] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 15 Aug 2025 13:56:46 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3585\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 3ad1b9ca-de50-4738-98e9-156f3ee9ecc2\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PWZouEERoAMEolw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689f3c9d-141d2447330cd1d502e846e2\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1009008\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-15T14:56:39+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => MICHAEL VAUGHAN RACKING SERVIC\n                            [addressLine1] => 3 Gibbs Close\n                            [city] => Swindon\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => SN3 5EW\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Michael\n                            [lastName] => Vaughan\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-16\n                                    [endDate] => 2026-09-15\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56862671431857\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-15\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-15\n                                                            [startDate] => 2025-09-16\n                                                            [endDate] => 2026-09-15\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1009008\n)\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:57:17
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => dea67d9e-9ea1-43dd-9081-d176b8dc03cd\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1009008\n            [transactionId] => f0e009d3-7c9d-57ca-ac76-68ca4253d1e2\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1009008 status changed to Quoted.\n            [modifiedAt] => 2025-08-15T13:57:14.325Z\n        )\n\n    [publishedAt] => 2025-08-15T13:57:14.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 13:57:17
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => dea67d9e-9ea1-43dd-9081-d176b8dc03cd\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1009008\n            [transactionId] => f0e009d3-7c9d-57ca-ac76-68ca4253d1e2\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1009008 status changed to Quoted.\n            [modifiedAt] => 2025-08-15T13:57:14.325Z\n        )\n\n    [publishedAt] => 2025-08-15T13:57:14.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 13:57:17] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1009008', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1009008', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 23:01:02
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 80d9da77-c140-4f55-b373-3eb7b5f2f408\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-936077\n            [transactionId] => 968ff399-bb5d-518d-a445-93681b69a0e4\n            [quoteStatus] => Expired\n            [message] => Quote# Q-936077 status changed to Expired.\n            [modifiedAt] => 2025-08-15T23:00:50.464Z\n        )\n\n    [publishedAt] => 2025-08-15T23:00:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-15 23:01:02
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 80d9da77-c140-4f55-b373-3eb7b5f2f408\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-936077\n            [transactionId] => 968ff399-bb5d-518d-a445-93681b69a0e4\n            [quoteStatus] => Expired\n            [message] => Quote# Q-936077 status changed to Expired.\n            [modifiedAt] => 2025-08-15T23:00:50.464Z\n        )\n\n    [publishedAt] => 2025-08-15T23:00:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-15 23:01:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-936077', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-936077', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-17 23:00:19
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-17 23:00:19
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 95bf2e0b-a900-42da-9dc6-757ea3e257a2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-942834\n            [transactionId] => ad162168-1492-5f57-ac3f-419c4a175397\n            [quoteStatus] => Expired\n            [message] => Quote# Q-942834 status changed to Expired.\n            [modifiedAt] => 2025-08-17T23:00:13.939Z\n        )\n\n    [publishedAt] => 2025-08-17T23:00:14.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 95bf2e0b-a900-42da-9dc6-757ea3e257a2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-942834\n            [transactionId] => ad162168-1492-5f57-ac3f-419c4a175397\n            [quoteStatus] => Expired\n            [message] => Quote# Q-942834 status changed to Expired.\n            [modifiedAt] => 2025-08-17T23:00:13.939Z\n        )\n\n    [publishedAt] => 2025-08-17T23:00:14.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-17 23:00:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-942834', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942834', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-17 23:00:35
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 440091ad-b922-4993-8d2f-1d55594c1501\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-942066\n            [transactionId] => 696e7c06-d4a7-5d7c-987b-1cf2cf3583ff\n            [quoteStatus] => Expired\n            [message] => Quote# Q-942066 status changed to Expired.\n            [modifiedAt] => 2025-08-17T23:00:28.380Z\n        )\n\n    [publishedAt] => 2025-08-17T23:00:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-17 23:00:35
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 440091ad-b922-4993-8d2f-1d55594c1501\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-942066\n            [transactionId] => 696e7c06-d4a7-5d7c-987b-1cf2cf3583ff\n            [quoteStatus] => Expired\n            [message] => Quote# Q-942066 status changed to Expired.\n            [modifiedAt] => 2025-08-17T23:00:28.380Z\n        )\n\n    [publishedAt] => 2025-08-17T23:00:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-17 23:00:35] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-942066', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-942066', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 08:24:11
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 56b6cfff-3072-4dd2-b193-9ca33d891f2c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-946524\n            [transactionId] => 1fae4ced-ccec-5644-b595-2c15ad85fb43\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-946524 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T08:24:08.687Z\n        )\n\n    [publishedAt] => 2025-08-18T08:24:09.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 08:24:11
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 56b6cfff-3072-4dd2-b193-9ca33d891f2c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-946524\n            [transactionId] => 1fae4ced-ccec-5644-b595-2c15ad85fb43\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-946524 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T08:24:08.687Z\n        )\n\n    [publishedAt] => 2025-08-18T08:24:09.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 08:24:11] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 08:24:12
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c36bf642-fd91-43f0-845b-f4f7f185ca93\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-946524\n            [transactionId] => 1fae4ced-ccec-5644-b595-2c15ad85fb43\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-946524 status changed to Ordered.\n            [modifiedAt] => 2025-08-18T08:24:09.422Z\n        )\n\n    [publishedAt] => 2025-08-18T08:24:10.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 08:24:12
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c36bf642-fd91-43f0-845b-f4f7f185ca93\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-946524\n            [transactionId] => 1fae4ced-ccec-5644-b595-2c15ad85fb43\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-946524 status changed to Ordered.\n            [modifiedAt] => 2025-08-18T08:24:09.422Z\n        )\n\n    [publishedAt] => 2025-08-18T08:24:10.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 08:24:12] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-946524', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-946524', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 09:49:31
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7ace77cc-b2d1-4253-9fc9-2ee90ed62b1e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995460\n            [transactionId] => c0894c4c-dae9-5350-af28-2d5f6272ab73\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-995460 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T09:49:28.131Z\n        )\n\n    [publishedAt] => 2025-08-18T09:49:28.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 09:49:31
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7ace77cc-b2d1-4253-9fc9-2ee90ed62b1e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995460\n            [transactionId] => c0894c4c-dae9-5350-af28-2d5f6272ab73\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-995460 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T09:49:28.131Z\n        )\n\n    [publishedAt] => 2025-08-18T09:49:28.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 09:49:31] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 09:49:32
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 041ba36d-68cf-4e16-a6cd-7bbe04638144\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995460\n            [transactionId] => c0894c4c-dae9-5350-af28-2d5f6272ab73\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-995460 status changed to Ordered.\n            [modifiedAt] => 2025-08-18T09:49:30.488Z\n        )\n\n    [publishedAt] => 2025-08-18T09:49:30.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 09:49:32
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 041ba36d-68cf-4e16-a6cd-7bbe04638144\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995460\n            [transactionId] => c0894c4c-dae9-5350-af28-2d5f6272ab73\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-995460 status changed to Ordered.\n            [modifiedAt] => 2025-08-18T09:49:30.488Z\n        )\n\n    [publishedAt] => 2025-08-18T09:49:30.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 09:49:32] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995460', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995460', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 10:00:14
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 614d607a-b729-4152-a936-43f1f6ff3106\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-992935\n            [transactionId] => 78671c3a-10ff-5a56-90cc-7d35c365807a\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-992935 status changed to Quoted.\n            [modifiedAt] => 2025-08-18T10:00:11.919Z\n        )\n\n    [publishedAt] => 2025-08-18T10:00:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 10:00:14
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 614d607a-b729-4152-a936-43f1f6ff3106\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-992935\n            [transactionId] => 78671c3a-10ff-5a56-90cc-7d35c365807a\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-992935 status changed to Quoted.\n            [modifiedAt] => 2025-08-18T10:00:11.919Z\n        )\n\n    [publishedAt] => 2025-08-18T10:00:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 10:00:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 10:01:22
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ab98f2d5-fd5c-4375-8134-b4459af1e85e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995862\n            [transactionId] => 1d338cb2-a9f8-5a9c-a396-eda17798a6a2\n            [quoteStatus] => Cancelled\n            [message] => Quote# Q-995862 status changed to Cancelled.\n            [modifiedAt] => 2025-08-18T10:01:19.161Z\n        )\n\n    [publishedAt] => 2025-08-18T10:01:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 10:01:22
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ab98f2d5-fd5c-4375-8134-b4459af1e85e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995862\n            [transactionId] => 1d338cb2-a9f8-5a9c-a396-eda17798a6a2\n            [quoteStatus] => Cancelled\n            [message] => Quote# Q-995862 status changed to Cancelled.\n            [modifiedAt] => 2025-08-18T10:01:19.161Z\n        )\n\n    [publishedAt] => 2025-08-18T10:01:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 10:01:22] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995862', quote_status = 'Cancelled' ON DUPLICATE KEY UPDATE quote_number = 'Q-995862', quote_status = 'Cancelled';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 11:03:18
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 89700b65-4a81-4d8a-90c7-da6a968c349a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Order Submitted\n            [message] => Quote# ********* status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T11:03:15.168Z\n        )\n\n    [publishedAt] => 2025-08-18T11:03:15.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 11:03:18
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 89700b65-4a81-4d8a-90c7-da6a968c349a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Order Submitted\n            [message] => Quote# ********* status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T11:03:15.168Z\n        )\n\n    [publishedAt] => 2025-08-18T11:03:15.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 11:03:18
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 246484ae-3971-45b4-a6c4-e275dbe9c70f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-18T11:03:15.991Z\n        )\n\n    [publishedAt] => 2025-08-18T11:03:16.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 11:03:18] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 11:03:19
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 246484ae-3971-45b4-a6c4-e275dbe9c70f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 095f7cfb-ee74-5b2e-812a-1de319441bb2\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-18T11:03:15.991Z\n        )\n\n    [publishedAt] => 2025-08-18T11:03:16.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 11:03:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 15:21:08
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8bbd3b6a-62dd-467c-8f93-199b0eca8236\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1006441\n            [transactionId] => a06ec259-3831-5d80-95fa-a34ea1ca9265\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1006441 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T15:21:05.668Z\n        )\n\n    [publishedAt] => 2025-08-18T15:21:06.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 15:21:08
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8bbd3b6a-62dd-467c-8f93-199b0eca8236\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1006441\n            [transactionId] => a06ec259-3831-5d80-95fa-a34ea1ca9265\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1006441 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-18T15:21:05.668Z\n        )\n\n    [publishedAt] => 2025-08-18T15:21:06.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 15:21:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 15:21:10
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c46f243c-58f7-43b8-8e1e-830faf47a639\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1006441\n            [transactionId] => a06ec259-3831-5d80-95fa-a34ea1ca9265\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1006441 status changed to Ordered.\n            [modifiedAt] => 2025-08-18T15:21:08.335Z\n        )\n\n    [publishedAt] => 2025-08-18T15:21:08.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-18 15:21:10] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-18 15:21:11
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c46f243c-58f7-43b8-8e1e-830faf47a639\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1006441\n            [transactionId] => a06ec259-3831-5d80-95fa-a34ea1ca9265\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1006441 status changed to Ordered.\n            [modifiedAt] => 2025-08-18T15:21:08.335Z\n        )\n\n    [publishedAt] => 2025-08-18T15:21:08.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-18 15:21:11] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1006441', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1006441', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-19 08:25:27] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:25:27
[quote_update] [2025-08-19 08:25:27] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:25:27] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 17ada605-702e-485d-94d4-da6bca7c9323\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-19T08:25:24.683Z\n        )\n\n    [publishedAt] => 2025-08-19T08:25:24.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:25:27] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:25:27
[quote_update] [2025-08-19 08:25:27] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:25:27] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 17ada605-702e-485d-94d4-da6bca7c9323\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-19T08:25:24.683Z\n        )\n\n    [publishedAt] => 2025-08-19T08:25:24.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:25:28] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 19 Aug 2025 08:25:28 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1638\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 9ca867bb-e830-4f72-a0a7-2dba740b3e9f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => Pi024FrqIAMEoYw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68a434f8-0d63460555574bd3276481ab\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-19T09:25:23+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => All Design (Scotland) Ltd\n                            [addressLine1] => Campus 2 2 Balgownie Drive\n                            [addressLine2] => Bridge Of Don\n                            [city] => Aberdeen\n                            [stateProvinceCode] => \n                            [stateProvince] => ABERDEENSHIRE\n                            [postalCode] => AB22 8GU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Paul\n                            [lastName] => Walber\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-19 08:25:29] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 19 Aug 2025 08:25:29 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1638\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 6a94db84-978a-41cd-b624-431ce8d8bc5c\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => Pi025GjFIAMEsYQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68a434f8-3589c49a1f9844241f029f52\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-19T09:25:23+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => All Design (Scotland) Ltd\n                            [addressLine1] => Campus 2 2 Balgownie Drive\n                            [addressLine2] => Bridge Of Don\n                            [city] => Aberdeen\n                            [stateProvinceCode] => \n                            [stateProvince] => ABERDEENSHIRE\n                            [postalCode] => AB22 8GU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Paul\n                            [lastName] => Walber\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:27:39
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 08134ee8-0697-4ea7-9cb7-9cad05646425\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-19T08:27:36.547Z\n        )\n\n    [publishedAt] => 2025-08-19T08:27:36.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:27:39
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 08134ee8-0697-4ea7-9cb7-9cad05646425\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-19T08:27:36.547Z\n        )\n\n    [publishedAt] => 2025-08-19T08:27:36.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-19 08:27:39] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-19 08:30:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:30:51
[quote_update] [2025-08-19 08:30:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:30:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 47ad5a68-ff32-4e21-a5be-5d9d822e3a34\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1015236\n            [transactionId] => 283342fb-4686-514f-a764-23e57ee93b8e\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1015236 status changed to Draft.\n            [modifiedAt] => 2025-08-19T08:30:48.979Z\n        )\n\n    [publishedAt] => 2025-08-19T08:30:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:30:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:30:51
[quote_update] [2025-08-19 08:30:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:30:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 47ad5a68-ff32-4e21-a5be-5d9d822e3a34\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1015236\n            [transactionId] => 283342fb-4686-514f-a764-23e57ee93b8e\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1015236 status changed to Draft.\n            [modifiedAt] => 2025-08-19T08:30:48.979Z\n        )\n\n    [publishedAt] => 2025-08-19T08:30:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:30:53] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 19 Aug 2025 08:30:53 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3610\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => ecaa82af-ce86-489c-8706-89fe7f30eb50\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => Pi1pkH6JoAMEuqg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68a4363c-0569fd201f98439c5181a84d\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1015236\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-19T09:30:47+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => LITAC ENGINEERING\n                            [addressLine1] => 160 Hainault Avenue\n                            [addressLine2] => Giffard Park\n                            [city] => Milton Keynes\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => MK14 5HZ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ian\n                            [lastName] => Norman\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-20\n                                    [endDate] => 2026-09-19\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 63213277735141\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-19\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-19\n                                                            [startDate] => 2025-09-20\n                                                            [endDate] => 2026-09-19\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1015236\n)\n
[quote_update] [2025-08-19 08:30:53] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 19 Aug 2025 08:30:53 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3610\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 544383ad-daeb-4266-8ddc-7cb5eceebe10\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => Pi1pkH9EIAMEkEw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68a4363c-1de81ee45265462a7e1d6d93\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1015236\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-19T09:30:47+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => LITAC ENGINEERING\n                            [addressLine1] => 160 Hainault Avenue\n                            [addressLine2] => Giffard Park\n                            [city] => Milton Keynes\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => MK14 5HZ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Ian\n                            [lastName] => Norman\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-20\n                                    [endDate] => 2026-09-19\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 63213277735141\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-19\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-19\n                                                            [startDate] => 2025-09-20\n                                                            [endDate] => 2026-09-19\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1015236\n)\n
[quote_update] [2025-08-19 08:31:30] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:31:30
[quote_update] [2025-08-19 08:31:30] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:31:30] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1faffdf8-211c-45bc-8cd7-60c93cca80cc\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-19T08:31:27.894Z\n        )\n\n    [publishedAt] => 2025-08-19T08:31:28.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:31:30] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:31:30
[quote_update] [2025-08-19 08:31:30] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:31:30] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1faffdf8-211c-45bc-8cd7-60c93cca80cc\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-19T08:31:27.894Z\n        )\n\n    [publishedAt] => 2025-08-19T08:31:28.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:31:31] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 19 Aug 2025 08:31:31 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1716\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 171815b8-f7af-40f2-87da-1d0b00a76cc9\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => Pi1vkEwaoAMEMrQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68a43663-13d38df27e91930f07731199\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-19T09:31:20+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Northumbria Healthcare NHS Foundation Trust\n                            [addressLine1] => Unit 7 Northumbria House 7-8 Silver\n                            [addressLine2] => Fox Way Cobalt Business Park\n                            [city] => Newcastle Upon Tyne\n                            [stateProvinceCode] => \n                            [stateProvince] => TYNE AND WEAR\n                            [postalCode] => NE27 0QJ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Gillian\n                            [lastName] => Finn\n                            [phone] => +447815504919\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-19 08:31:31] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 19 Aug 2025 08:31:31 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1716\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 31e68d9c-0641-4e78-a350-c5c8560cd814\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => Pi1vlERHoAMETeg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68a43663-140b87e8437ba63040af738b\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-19T09:31:20+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Northumbria Healthcare NHS Foundation Trust\n                            [addressLine1] => Unit 7 Northumbria House 7-8 Silver\n                            [addressLine2] => Fox Way Cobalt Business Park\n                            [city] => Newcastle Upon Tyne\n                            [stateProvinceCode] => \n                            [stateProvince] => TYNE AND WEAR\n                            [postalCode] => NE27 0QJ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Gillian\n                            [lastName] => Finn\n                            [phone] => +447815504919\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:33:03
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 046e2e4c-32ea-4b45-abfe-efbfb3c6d31d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-19T08:33:01.024Z\n        )\n\n    [publishedAt] => 2025-08-19T08:33:01.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-19 08:33:03] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:33:04
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 046e2e4c-32ea-4b45-abfe-efbfb3c6d31d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 2cd05ef7-756d-5e70-a6d6-3fe94dfcfcb7\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-08-19T08:33:01.024Z\n        )\n\n    [publishedAt] => 2025-08-19T08:33:01.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-19 08:33:04] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:34:28
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 3c3fd46d-6431-4fd2-a1ec-e20008ea89cc\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Order Submitted\n            [message] => Quote# ********* status changed to Order Submitted.\n            [modifiedAt] => 2025-08-19T08:34:25.855Z\n        )\n\n    [publishedAt] => 2025-08-19T08:34:26.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:34:28
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 3c3fd46d-6431-4fd2-a1ec-e20008ea89cc\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Order Submitted\n            [message] => Quote# ********* status changed to Order Submitted.\n            [modifiedAt] => 2025-08-19T08:34:25.855Z\n        )\n\n    [publishedAt] => 2025-08-19T08:34:26.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-19 08:34:28] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:34:29
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => b2e18bac-02b6-491a-86a8-faf6c50e46c3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-19T08:34:26.787Z\n        )\n\n    [publishedAt] => 2025-08-19T08:34:27.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 08:34:29
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => b2e18bac-02b6-491a-86a8-faf6c50e46c3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 9de4051a-c562-5e2f-84a6-6404f253fd52\n            [quoteStatus] => Ordered\n            [message] => Quote# ********* status changed to Ordered.\n            [modifiedAt] => 2025-08-19T08:34:26.787Z\n        )\n\n    [publishedAt] => 2025-08-19T08:34:27.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-19 08:34:29] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 10:04:35
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c9655908-b476-4ae1-b086-17fe8fa4881f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-992935\n            [transactionId] => 78671c3a-10ff-5a56-90cc-7d35c365807a\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-992935 status changed to Ordered.\n            [modifiedAt] => 2025-08-19T10:04:33.310Z\n        )\n\n    [publishedAt] => 2025-08-19T10:04:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 10:04:35
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c9655908-b476-4ae1-b086-17fe8fa4881f\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-992935\n            [transactionId] => 78671c3a-10ff-5a56-90cc-7d35c365807a\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-992935 status changed to Ordered.\n            [modifiedAt] => 2025-08-19T10:04:33.310Z\n        )\n\n    [publishedAt] => 2025-08-19T10:04:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-19 10:04:35] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-992935', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-992935', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 10:07:19
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 533ba59d-da02-4e09-852b-f82a7f802ab1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-990570\n            [transactionId] => bad6bd08-c710-54e2-ac20-dc066233b82a\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-990570 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-19T10:07:17.026Z\n        )\n\n    [publishedAt] => 2025-08-19T10:07:17.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 10:07:19
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 533ba59d-da02-4e09-852b-f82a7f802ab1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-990570\n            [transactionId] => bad6bd08-c710-54e2-ac20-dc066233b82a\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-990570 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-19T10:07:17.026Z\n        )\n\n    [publishedAt] => 2025-08-19T10:07:17.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-19 10:07:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 10:07:22
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 11ab2ad1-e875-4d9a-b240-a195b878a07c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-990570\n            [transactionId] => bad6bd08-c710-54e2-ac20-dc066233b82a\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-990570 status changed to Ordered.\n            [modifiedAt] => 2025-08-19T10:07:20.320Z\n        )\n\n    [publishedAt] => 2025-08-19T10:07:20.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-19 10:07:22] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-19 10:07:23
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 11ab2ad1-e875-4d9a-b240-a195b878a07c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-990570\n            [transactionId] => bad6bd08-c710-54e2-ac20-dc066233b82a\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-990570 status changed to Ordered.\n            [modifiedAt] => 2025-08-19T10:07:20.320Z\n        )\n\n    [publishedAt] => 2025-08-19T10:07:20.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-19 10:07:23] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-990570', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-990570', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
