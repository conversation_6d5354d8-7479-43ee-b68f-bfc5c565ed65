<?php
/**
 * Test script to demonstrate fallback field matching functionality
 * Access via browser: http://localhost/autobooks/test_fallback_matching.php
 */

header('Content-Type: text/plain');

echo "=== Testing Fallback Field Matching ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the startup sequence to initialize everything
    require_once 'system/startup_sequence.php';
    
    echo "1. System initialized successfully\n\n";
    
    // Test scenario: Create a situation where fallback would be needed
    echo "2. Testing fallback scenario...\n";
    
    // Simulate columns that might conflict
    $test_columns = [
        'company_name',           // Should match to company_name field (priority 2)
        'end_customer_name',      // Also wants company_name field - conflict!
        'customer_company',       // Also wants company_name field - conflict!
        'business_name',          // Also wants company_name field - conflict!
        'end_customer_address_1', // Should match to address field (priority 1)
        'customer_email',         // Should match to email field
        'product_name',           // Should match to product_name field
        'subscription_id'         // Should match to subscription_reference field
    ];
    
    echo "Test columns: " . implode(', ', $test_columns) . "\n\n";
    
    // Get field mappings and test suggestions
    echo "3. Getting field suggestions with fallback...\n";
    $suggestions = system\unified_field_mapper::suggest_field_mappings($test_columns);
    
    foreach ($test_columns as $column) {
        if (isset($suggestions[$column])) {
            $suggestion = $suggestions[$column];
            echo "\n--- {$column} ---\n";
            echo "Primary match: {$suggestion['field_name']} (confidence: {$suggestion['confidence']}, score: {$suggestion['final_score']}, priority: {$suggestion['priority']})\n";
            
            if (!empty($suggestion['alternatives'])) {
                echo "Alternatives available:\n";
                foreach ($suggestion['alternatives'] as $i => $alt) {
                    echo "  " . ($i + 1) . ". {$alt['field_name']} (confidence: {$alt['confidence']}, score: {$alt['final_score']}, priority: {$alt['priority']})\n";
                }
            } else {
                echo "No alternatives found\n";
            }
        } else {
            echo "\n--- {$column} ---\n";
            echo "No match found\n";
        }
    }
    
    echo "\n\n4. Testing unified field aliases generation with fallback...\n";
    
    // Create a test table scenario
    $test_table = 'autobooks_import_sketchup_data'; // Use existing table
    $unified_mappings = [
        'min_confidence' => 75,
        'enable_fallback' => true,
        'fallback_min_confidence' => 60,
        'overrides' => []
    ];
    $table_aliases_config = ['autobooks_import_sketchup_data' => 'sketchup'];
    
    echo "Generating aliases for table: {$test_table}\n";
    echo "Fallback enabled: " . ($unified_mappings['enable_fallback'] ? 'YES' : 'NO') . "\n";
    echo "Primary confidence threshold: {$unified_mappings['min_confidence']}\n";
    echo "Fallback confidence threshold: {$unified_mappings['fallback_min_confidence']}\n\n";
    
    $aliases = system\data_source_manager::generate_unified_field_aliases($test_table, $unified_mappings, $table_aliases_config);
    
    echo "Generated " . count($aliases) . " aliases:\n";
    
    // Group aliases by their target field for better readability
    $grouped_aliases = [];
    foreach ($aliases as $column_key => $target_field) {
        if (!isset($grouped_aliases[$target_field])) {
            $grouped_aliases[$target_field] = [];
        }
        $grouped_aliases[$target_field][] = $column_key;
    }
    
    foreach ($grouped_aliases as $target_field => $column_keys) {
        echo "\n{$target_field}:\n";
        foreach ($column_keys as $column_key) {
            echo "  - {$column_key}\n";
        }
    }
    
    echo "\n\n5. Checking logs for fallback activity...\n";
    
    // Check recent logs for fallback messages
    $log_file = 'system/logs/data_source_manager_logfile.log';
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $log_lines = explode("\n", $log_content);
        $recent_lines = array_slice($log_lines, -20); // Last 20 lines
        
        $fallback_lines = array_filter($recent_lines, function($line) {
            return strpos($line, 'Fallback') !== false || strpos($line, 'fallback') !== false;
        });
        
        if (!empty($fallback_lines)) {
            echo "Recent fallback activity:\n";
            foreach ($fallback_lines as $line) {
                echo "  " . trim($line) . "\n";
            }
        } else {
            echo "No recent fallback activity found in logs\n";
        }
    } else {
        echo "Log file not found: {$log_file}\n";
    }
    
    echo "\n=== Test Complete ===\n";
    echo "✅ Fallback field matching system is now active\n";
    echo "✅ When primary field choices conflict, the system will try alternatives\n";
    echo "✅ Fallback uses a lower confidence threshold to find suitable alternatives\n";
    echo "✅ All fallback activity is logged for debugging\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Test ===\n";
?>
